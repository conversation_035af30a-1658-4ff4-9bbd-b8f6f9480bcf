<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 180 180" width="180" height="180">
  <defs>
    <linearGradient id="appleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  <!-- Background with rounded corners for iOS -->
  <rect x="0" y="0" width="180" height="180" rx="35" ry="35" fill="url(#appleGrad)"/>
  <!-- NH Text -->
  <text x="90" y="115" font-family="Arial, sans-serif" font-size="68" font-weight="bold" fill="white" text-anchor="middle">NH</text>
  <!-- Small tagline -->
  <text x="90" y="145" font-family="Arial, sans-serif" font-size="20" fill="white" text-anchor="middle" fill-opacity="0.9">Classes</text>
</svg>
