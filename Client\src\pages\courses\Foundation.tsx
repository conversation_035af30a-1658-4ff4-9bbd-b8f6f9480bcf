import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, Users, BookOpen, CheckCircle, Calendar, Target, Zap } from 'lucide-react';

const Foundation = () => {
  const courseFeatures = [
    "Complete NEET/JEE syllabus coverage integrated with CBSE/State Board preparation",
    "Latest NTA pattern alignment (2024-25) with updated syllabus and weightage",
    "PCM/PCB stream flexibility with option to switch based on performance",
    "Comprehensive chapter-wise tests, monthly assessments, and board mock exams",
    "Advanced problem-solving techniques and conceptual clarity focus",
    "Regular doubt clearing sessions, individual mentorship, and progress tracking",
    "Extensive study material, previous year papers, and practice question banks",
    "Strategic preparation timeline balancing board exams and competitive tests"
  ];

  const syllabusNEET = {
    "Physics": ["Mechanics", "Thermodynamics", "Optics", "Electricity & Magnetism", "Modern Physics", "Waves & Oscillations"],
    "Chemistry": ["Physical Chemistry", "Organic Chemistry", "Inorganic Chemistry", "Environmental Chemistry", "Biomolecules", "Polymers"],
    "Biology": ["Diversity of Living World", "Structural Organisation", "Cell Structure", "Plant Physiology", "Human Physiology", "Reproduction", "Genetics", "Evolution", "Ecology"]
  };

  const syllabusJEE = {
    "Physics": ["Mechanics", "Heat & Thermodynamics", "Waves & Oscillations", "Electricity & Magnetism", "Optics", "Modern Physics"],
    "Chemistry": ["Physical Chemistry", "Inorganic Chemistry", "Organic Chemistry", "Coordination Compounds", "Chemical Kinetics", "Electrochemistry"],
    "Mathematics": ["Algebra", "Trigonometry", "Coordinate Geometry", "Calculus", "Vector & 3D Geometry", "Probability & Statistics"]
  };

  const batchDetails = [
    { timing: "NEET Morning", time: "6:00 AM - 10:00 AM", days: "Monday to Saturday", focus: "PCB Focus" },
    { timing: "JEE Morning", time: "6:00 AM - 10:00 AM", days: "Monday to Saturday", focus: "PCM Focus" },
    { timing: "NEET Evening", time: "3:00 PM - 7:00 PM", days: "Monday to Saturday", focus: "PCB Focus" },
    { timing: "JEE Evening", time: "3:00 PM - 7:00 PM", days: "Monday to Saturday", focus: "PCM Focus" }
  ];

  const achievements = [
    { metric: "95%", description: "Board Exam Success Rate" },
    { metric: "87%", description: "NEET Qualification Rate" },
    { metric: "82%", description: "JEE Success Rate" },
    { metric: "200+", description: "Current Students" }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Header */}
      <section className="bg-gradient-to-r from-purple-600 to-violet-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/courses" className="inline-flex items-center text-purple-100 hover:text-white mb-6 transition-colors">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Courses
          </Link>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Foundation Course</h1>
              <p className="text-xl text-purple-100 mb-6">Class XI & XII - Board + NEET/JEE Integrated</p>
              <p className="text-lg text-purple-50 leading-relaxed mb-8">
                The perfect blend of Board exam excellence and competitive exam preparation. Our integrated approach 
                ensures you excel in both CBSE/State Board exams and crack NEET/JEE with confidence.
              </p>
              
              <div className="flex flex-wrap gap-4 mb-8">
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Clock className="h-5 w-5 inline mr-2" />
                  2 Years Duration
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Users className="h-5 w-5 inline mr-2" />
                  200+ Students
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <BookOpen className="h-5 w-5 inline mr-2" />
                  Dual Focus
                </div>
              </div>
              
              <Link 
                to="/admission" 
                className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
              >
                Enroll Now
                <ArrowLeft className="h-5 w-5 ml-2 rotate-180" />
              </Link>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400"
                alt="Foundation Course students in classroom"
                className="rounded-2xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Our Success Record</h2>
          <div className="grid md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-purple-600 mb-2">{achievement.metric}</div>
                <div className="text-gray-600">{achievement.description}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Course Highlights</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {courseFeatures.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-purple-500 mt-1 flex-shrink-0" />
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Syllabus Tabs */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Detailed Syllabus</h2>
          
          {/* NEET Syllabus */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-purple-600 mb-6 flex items-center">
              <Target className="h-6 w-6 mr-2" />
              NEET Track (PCB)
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              {Object.entries(syllabusNEET).map(([subject, topics]) => (
                <div key={subject} className="bg-purple-50 rounded-xl p-6">
                  <h4 className="text-lg font-bold text-purple-700 mb-4">{subject}</h4>
                  <ul className="space-y-2">
                    {topics.map((topic, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                        {topic}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* JEE Syllabus */}
          <div>
            <h3 className="text-2xl font-bold text-indigo-600 mb-6 flex items-center">
              <Zap className="h-6 w-6 mr-2" />
              JEE Track (PCM)
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              {Object.entries(syllabusJEE).map(([subject, topics]) => (
                <div key={subject} className="bg-indigo-50 rounded-xl p-6">
                  <h4 className="text-lg font-bold text-indigo-700 mb-4">{subject}</h4>
                  <ul className="space-y-2">
                    {topics.map((topic, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <div className="w-2 h-2 bg-indigo-400 rounded-full mr-3"></div>
                        {topic}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Batch Timings */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Available Batches</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center">
                <Calendar className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">{batch.timing}</h3>
                <p className="text-purple-600 font-medium mb-2">{batch.time}</p>
                <p className="text-gray-600 mb-2">{batch.days}</p>
                <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">{batch.focus}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-purple-600 to-violet-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Excel in Both Board & Competitive Exams?</h2>
          <p className="text-xl text-purple-100 mb-8">
            Join our Foundation Course and experience the perfect balance of academic excellence and competitive success.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/admission"
              className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Enroll Now
            </Link>
            <Link 
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors"
            >
              Schedule Visit
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Foundation;
