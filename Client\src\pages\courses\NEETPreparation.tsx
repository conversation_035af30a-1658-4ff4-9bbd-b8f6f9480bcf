import { ArrowLeft, BookOpen, Users, Clock, Award, CheckCircle, Star, Trophy, Target, Zap, Calendar, Phone } from 'lucide-react';
import { Link } from 'react-router-dom';

const NEETPreparation = () => {
  const courseHighlights = [
    "India's premier medical entrance examination",
    "Gateway to MBBS, BDS, AYUSH courses",
    "Conducted by National Testing Agency (NTA)",
    "Single exam for all medical colleges",
    "Over 15 lakh students appear annually"
  ];

  const syllabusTopics = {
    Physics: [
      "Mechanics & Properties of Matter",
      "Heat & Thermodynamics", 
      "Waves & Oscillations",
      "Electrostatics & Current Electricity",
      "Magnetism & Electromagnetic Induction",
      "Optics & Modern Physics",
      "Dual Nature of Matter & Radiation",
      "Atoms, Nuclei & Electronic Devices"
    ],
    Chemistry: [
      "Some Basic Concepts in Chemistry",
      "Atomic Structure & Chemical Bonding",
      "States of Matter & Thermodynamics", 
      "Chemical Equilibrium & Redox Reactions",
      "Hydrogen, s & p Block Elements",
      "d & f Block Elements & Coordination",
      "Organic Chemistry Fundamentals",
      "Biomolecules & Chemistry in Everyday Life"
    ],
    Biology: [
      "Diversity in Living World",
      "Structural Organization in Animals & Plants",
      "Cell Structure & Function",
      "Plant Physiology & Human Physiology",
      "Reproduction & Development",
      "Genetics & Evolution",
      "Biology & Human Welfare",
      "Biotechnology & Ecology"
    ]
  };
  const facultyMembers = [
    {
      name: "Dr. Priya Sharma",
      subject: "Biology",
      experience: "12+ Years",
      qualification: "Ph.D. Biology, AIIMS Delhi",
      specialization: "Genetics & Molecular Biology",
      achievements: "Expert in Biology Teaching"
    },
    {
      name: "Prof. Rajesh Kumar",
      subject: "Chemistry", 
      experience: "15+ Years",
      qualification: "M.Sc. Chemistry, IIT Kanpur",
      specialization: "Organic & Inorganic Chemistry",
      achievements: "Chemistry Subject Expert"
    },
    {
      name: "Dr. Amit Singh",
      subject: "Physics",
      experience: "10+ Years", 
      qualification: "Ph.D. Physics, IIT Delhi",
      specialization: "Modern Physics & Mechanics",
      achievements: "Physics Teaching Specialist"
    }
  ];

  const studyMaterial = [
    {
      title: "NCERT Comprehensive Notes",
      description: "Detailed chapter-wise notes covering complete NCERT syllabus",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Question Banks & Previous Papers",
      description: "15+ years NEET previous questions with detailed solutions", 
      icon: <Award className="h-6 w-6" />
    },
    {
      title: "Mock Test Series",
      description: "Weekly full-length tests following exact NEET pattern",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Formula Sheets & Quick Revision",
      description: "Subject-wise formula compilations and rapid revision notes",
      icon: <Zap className="h-6 w-6" />
    }
  ];
  const batchDetails = [
    {
      name: "Foundation Batch",
      duration: "2 Years (Class 11-12)",
      timing: "Morning: 6:00 AM - 12:00 PM",
      strength: "30 Students",
      features: ["Complete Syllabus Coverage", "Board + NEET Preparation", "Daily Practice Tests"]
    },
    {
      name: "Target Batch", 
      duration: "1 Year (Class 12/Dropper)",
      timing: "Afternoon: 1:00 PM - 7:00 PM",
      strength: "25 Students", 
      features: ["Intensive Coaching", "Advanced Problem Solving", "Weekly Mock Tests"]
    },
    {
      name: "Crash Course",
      duration: "3-6 Months",
      timing: "Evening: 5:00 PM - 9:00 PM", 
      strength: "20 Students",
      features: ["High-Yield Topics", "Exam Strategies", "Time Management"]
    }
  ];

  return (
    <div className="min-h-screen pt-16">      {/* Enhanced Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-8">
            <Link to="/courses" className="flex items-center text-green-600 hover:text-green-700 font-medium group">
              <ArrowLeft className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
              Back to Courses
            </Link>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-4 rounded-2xl mr-4 shadow-lg">
                  <BookOpen className="h-10 w-10 text-white" />
                </div>
                <div>
                  <span className="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
                    🏥 Medical Entrance Excellence
                  </span>
                  <div className="text-sm text-gray-600 mt-1">India's Premier Medical Gateway</div>
                </div>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  NEET-UG
                </span>
                <br />
                <span className="text-4xl md:text-5xl">Mastery Program</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                🎯 <strong>Conquer India's toughest medical entrance</strong> with our comprehensive NEET-UG preparation program
                <br/>
                🏥 Your gateway to <strong>MBBS, BDS, and AYUSH</strong> admissions across India
                <br/>
                📚 Experience <strong>expert guidance</strong> from seasoned medical entrance professionals
              </p>
              
              {/* Key Success Metrics */}
              <div className="grid grid-cols-3 gap-4 mb-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-green-100">
                  <div className="text-2xl font-bold text-green-600 mb-1">180</div>
                  <div className="text-sm font-medium text-gray-700">Total Questions</div>
                </div>
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-emerald-100">
                  <div className="text-2xl font-bold text-emerald-600 mb-1">720</div>
                  <div className="text-sm font-medium text-gray-700">Maximum Marks</div>
                </div>
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-teal-100">
                  <div className="text-2xl font-bold text-teal-600 mb-1">3h 20m</div>
                  <div className="text-sm font-medium text-gray-700">Exam Duration</div>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <Link
                  to="/admission"
                  className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 text-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  🚀 Begin Your Medical Journey
                </Link>
                <a
                  href="tel:+919058619887"
                  className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-xl font-semibold hover:bg-green-50 hover:border-green-700 transition-all duration-300 text-center flex items-center justify-center"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  📞 Get Expert Consultation
                </a>
              </div>
              
              {/* Trust Indicators */}
              <div className="flex items-center space-x-6 text-sm text-gray-600">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  <span>✅ Admissions Open Now</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse"></div>
                  <span>👨‍⚕️ Expert Medical Faculty</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              {/* Enhanced Image Grid */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <img
                    src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Medical Students with NCERT Biology Books for NEET Preparation"
                    className="w-full h-48 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                  <img
                    src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Medical Reference Books and Study Materials for NEET"
                    className="w-full h-40 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                </div>
                <div className="space-y-4 mt-8">
                  <img
                    src="https://images.unsplash.com/photo-1582560475093-ba66accbc424?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Physics Chemistry Biology Learning for Medical Entrance"
                    className="w-full h-40 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                  <img
                    src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Medical Students Intensive Study Session for NEET"
                    className="w-full h-48 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                </div>
              </div>
              
              {/* Floating Achievement Badges */}
              <div className="absolute -top-6 -left-6 bg-white rounded-xl p-3 shadow-lg border border-green-100">
                <div className="flex items-center">
                  <Trophy className="w-6 h-6 text-green-600 mr-2" />
                  <div>
                    <p className="text-sm font-semibold text-gray-900">NEET Success</p>
                    <p className="text-xs text-green-600">Top Rankings</p>
                  </div>
                </div>
              </div>
              
              <div className="absolute -bottom-6 -right-6 bg-white rounded-xl p-3 shadow-lg border border-emerald-100">
                <div className="flex items-center">
                  <Users className="w-6 h-6 text-emerald-600 mr-2" />
                  <div>
                    <p className="text-sm font-semibold text-gray-900">Expert Faculty</p>
                    <p className="text-xs text-emerald-600">Medical Professionals</p>
                  </div>
                </div>
              </div>
              
              <div className="absolute top-1/2 -right-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white p-3 rounded-xl shadow-lg animate-bounce">
                <div className="text-center">
                  <div className="text-lg font-bold">🏥</div>
                  <div className="text-xs">Medical Dreams</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Course Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              About NEET-UG
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know about India's most prestigious medical entrance examination
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-gradient-to-br from-green-50 to-teal-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Exam Details</h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <span><strong>Duration:</strong> 3 hours 20 minutes</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <span><strong>Questions:</strong> 180 (50 each subject)</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <span><strong>Maximum Marks:</strong> 720 (180 × 4)</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <span><strong>Negative Marking:</strong> -1 for wrong answers</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <span><strong>Mode:</strong> Offline (Pen & Paper)</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Key Highlights</h3>
              <ul className="space-y-3">
                {courseHighlights.map((highlight, index) => (
                  <li key={index} className="flex items-start">
                    <Star className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span>{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Syllabus Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Complete Syllabus Coverage
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive preparation for all three subjects as per NEET syllabus
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {Object.entries(syllabusTopics).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center mb-6">
                  <div className={`p-3 rounded-xl mr-4 ${
                    subject === 'Physics' ? 'bg-blue-100' : 
                    subject === 'Chemistry' ? 'bg-purple-100' : 'bg-green-100'
                  }`}>
                    <BookOpen className={`h-6 w-6 ${
                      subject === 'Physics' ? 'text-blue-600' : 
                      subject === 'Chemistry' ? 'text-purple-600' : 'text-green-600'
                    }`} />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{subject}</h3>
                </div>
                
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-start text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Expert Faculty Team
            </h2>
            <p className="text-xl text-gray-600">
              Learn from experienced educators with proven track records
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-6 shadow-lg text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-10 w-10 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{faculty.name}</h3>
                <div className="text-green-600 font-semibold mb-2">{faculty.subject}</div>
                <div className="text-sm text-gray-600 mb-4">
                  <div>{faculty.qualification}</div>
                  <div>{faculty.experience}</div>
                </div>
                
                <div className="bg-white p-3 rounded-lg mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-1">Specialization</div>
                  <div className="text-sm text-gray-600">{faculty.specialization}</div>
                </div>
                
                <div className="flex items-center justify-center text-sm text-green-600 font-semibold">
                  <Trophy className="h-4 w-4 mr-1" />
                  {faculty.achievements}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Study Material
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need for successful NEET preparation
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <div className="text-green-600">{material.icon}</div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600 text-sm">{material.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Batches
            </h2>
            <p className="text-xl text-gray-600">
              Choose the batch that best fits your preparation timeline
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-6 shadow-lg">
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{batch.name}</h3>
                </div>
                
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Duration:</strong> {batch.duration}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Timing:</strong> {batch.timing}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Users className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Batch Size:</strong> {batch.strength}</span>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Link
                  to="/admission"
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 text-center block"
                >
                  Select This Batch
                </Link>
              </div>
            ))}
          </div>        </div>
      </section>

      {/* NEET Study Environment */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                NCERT-Based NEET Preparation
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Our NEET preparation is completely based on NCERT curriculum with additional practice materials 
                to ensure comprehensive coverage of the medical entrance syllabus.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
                  <span className="text-gray-700">NCERT Biology, Physics & Chemistry textbooks</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
                  <span className="text-gray-700">Medical entrance exam pattern practice</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
                  <span className="text-gray-700">Interactive classroom learning environment</span>
                </div>
              </div>
            </div>
            <div>
              <img
                src="https://images.pexels.com/photos/4792728/pexels-photo-4792728.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="Medical students studying NCERT books for NEET preparation in coaching institute"
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-teal-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Start Your NEET Journey?
          </h2>
          <p className="text-xl text-green-100 mb-8">
            Join New Heights Classes and transform your medical dreams into reality
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Apply Now
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors duration-200"
            >
              Get Free Counseling
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NEETPreparation;
