import { Book<PERSON>pen, Users, Clock, Target, Award, CheckCircle, Brain, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';

const TwoYearProgram = () => {
  const syllabusData = {
    "Physics (Class 11)": [
      "Physical World & Units",
      "Kinematics & Laws of Motion", 
      "Work, Energy & Power",
      "Rotational Motion",
      "Gravitation & Oscillations",
      "Mechanical Properties of Solids & Fluids",
      "Thermal Properties & Thermodynamics",
      "Kinetic Theory & Wave Motion"
    ],
    "Physics (Class 12)": [
      "Electric Charges & Fields",
      "Electrostatic Potential & Current Electricity",
      "Magnetic Effects & Electromagnetic Induction",
      "Alternating Current & EM Waves",
      "Ray Optics & Wave Optics",
      "Dual Nature of Radiation & Atoms",
      "Nuclei & Semiconductor Electronics"
    ],
    "Chemistry (Class 11)": [
      "Some Basic Concepts & Atomic Structure",
      "Chemical Bonding & Molecular Structure",
      "States of Matter & Thermodynamics",
      "Equilibrium & Redox Reactions",
      "Hydrogen & s-Block Elements",
      "p-Block Elements & Organic Chemistry",
      "Hydrocarbons & Environmental Chemistry"
    ],
    "Chemistry (Class 12)": [
      "Solutions & Electrochemistry",
      "Chemical Kinetics & Surface Chemistry",
      "p-Block Elements & d & f Block Elements",
      "Coordination Compounds",
      "Aldehydes, Ketones & Carboxylic Acids",
      "Amines & Biomolecules",
      "Polymers & Chemistry in Everyday Life"
    ],
    "Mathematics (Class 11)": [
      "Sets, Relations & Functions",
      "Trigonometric Functions & Identities",
      "Principle of Mathematical Induction",
      "Complex Numbers & Linear Inequalities",
      "Permutations & Combinations",
      "Binomial Theorem & Sequences & Series",
      "Straight Lines & Conic Sections",
      "Introduction to 3D Geometry",
      "Limits & Derivatives",
      "Statistics & Probability"
    ],
    "Mathematics (Class 12)": [
      "Relations & Functions",
      "Inverse Trigonometric Functions",
      "Matrices & Determinants",
      "Continuity & Differentiability",
      "Applications of Derivatives",
      "Integrals & Applications of Integrals",
      "Differential Equations",
      "Vector Algebra & 3D Geometry",
      "Linear Programming & Probability"
    ],
    "Biology (Class 11)": [
      "Diversity in Living World",
      "Structural Organization in Animals & Plants",
      "Cell Structure & Function",
      "Plant Physiology",
      "Human Physiology"
    ],
    "Biology (Class 12)": [
      "Reproduction",
      "Genetics & Evolution",
      "Biology & Human Welfare",
      "Biotechnology & Its Applications",
      "Ecology & Environment"
    ]
  };

  const facultyMembers = [
    {
      name: "Dr. Vikram Singh",
      subject: "Physics",
      experience: "15+ Years",
      qualification: "Ph.D. Physics, IIT Delhi",
      specialization: "Mechanics & Modern Physics",
      achievements: "Physics Subject Expert"
    },
    {
      name: "Prof. Anita Sharma",
      subject: "Chemistry", 
      experience: "18+ Years",
      qualification: "M.Sc. Chemistry, IIT Kanpur",
      specialization: "Organic & Inorganic Chemistry",
      achievements: "Chemistry Teaching Specialist"
    },
    {
      name: "Dr. Rajesh Kumar",
      subject: "Mathematics",
      experience: "20+ Years", 
      qualification: "Ph.D. Mathematics, IIT Bombay",
      specialization: "Calculus & Coordinate Geometry",
      achievements: "Mathematics Expert"
    },
    {
      name: "Prof. Priya Gupta",
      subject: "Biology",
      experience: "14+ Years",
      qualification: "M.Sc. Botany, Ph.D. Genetics",
      specialization: "Genetics & Plant Physiology",
      achievements: "Biology Subject Specialist"
    }
  ];

  const studyMaterial = [
    {
      title: "NCERT Comprehensive Notes",
      description: "Complete chapter-wise notes covering NCERT syllabus for Classes 11 & 12",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Board Exam Preparation",
      description: "Specialized material for CBSE/State board exam preparation",
      icon: <Award className="h-6 w-6" />
    },
    {
      title: "Competitive Exam Focus",
      description: "Additional problems and concepts for NEET/JEE preparation",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Mock Test Series",
      description: "Regular board pattern and competitive exam pattern tests",
      icon: <Zap className="h-6 w-6" />
    }
  ];

  const successStats = [
    { number: "New", label: "Institute", subtext: "Established 2025" },
    { number: "Expert", label: "Faculty", subtext: "IIT/NIT Alumni" },
    { number: "2-Year", label: "Program", subtext: "Comprehensive Coverage" },
    { number: "Quality", label: "Education", subtext: "Modern Teaching" }
  ];

  const batchDetails = [
    {
      name: "PCM Batch (JEE Focus)",
      duration: "2 Years (Class 11-12)",
      timing: "Morning: 6:30 AM - 12:30 PM",
      strength: "30 Students",
      features: ["Physics, Chemistry, Mathematics", "JEE Main & Advanced Focus", "Board Exam Preparation"]
    },
    {
      name: "PCB Batch (NEET Focus)", 
      duration: "2 Years (Class 11-12)",
      timing: "Afternoon: 1:00 PM - 7:00 PM",
      strength: "30 Students",
      features: ["Physics, Chemistry, Biology", "NEET-UG Preparation", "Medical Entrance Training"]
    },
    {
      name: "PCMB Batch (All Options)",
      duration: "2 Years (Class 11-12)",
      timing: "Evening: 3:00 PM - 9:00 PM", 
      strength: "25 Students",
      features: ["All Four Subjects", "Multiple Career Options", "Flexibility in Stream Choice"]
    }
  ];

  const features = [
    {
      icon: <Users className="h-8 w-8 text-purple-600" />,
      title: "Expert Faculty",
      description: "IIT/NIT alumni with extensive teaching experience"
    },
    {
      icon: <BookOpen className="h-8 w-8 text-purple-600" />,
      title: "Comprehensive Curriculum",
      description: "Complete Class 11 & 12 syllabus with competitive exam preparation"
    },
    {
      icon: <Target className="h-8 w-8 text-purple-600" />,
      title: "Dual Focus",
      description: "Board exam excellence with NEET/JEE preparation"
    },
    {
      icon: <Award className="h-8 w-8 text-purple-600" />,
      title: "Regular Assessments",
      description: "Monthly tests, board pattern exams, and competitive exam mock tests"
    }
  ];

  const benefits = [
    "Complete 11th & 12th syllabus coverage",
    "Board exam and competitive exam preparation",
    "Stream flexibility with PCM/PCB/PCMB options",
    "Regular doubt clearing sessions",
    "Individual attention in small batches",
    "Comprehensive study material and notes",
    "Mock tests and performance analysis",
    "Career guidance and counseling",
    "Parent-teacher meetings and progress reports"
  ];

  return (
    <div className="min-h-screen pt-16">      {/* Enhanced Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Hero Content */}
            <div className="text-center lg:text-left">
              <div className="inline-flex items-center bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Clock className="w-4 h-4 mr-2" />
                Complete 2-Year Journey • Class XI & XII
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Master Your
                <span className="bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent block">
                  Academic Future
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-2xl">
                Transform your potential into success with our comprehensive Two-Year Program. Experience the perfect blend of 
                <span className="font-semibold text-purple-600"> board exam mastery</span> and 
                <span className="font-semibold text-green-600"> competitive exam preparation</span> 
                that sets you up for multiple career pathways.
              </p>
              
              {/* Key Highlights */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-purple-100">
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-3">
                    <Target className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">Dual Excellence</h3>
                  <p className="text-sm text-gray-600">Board + Competitive Exam Success</p>
                </div>
                
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-green-100">
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-3">
                    <Brain className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">Smart Learning</h3>
                  <p className="text-sm text-gray-600">NCERT to Advanced Level</p>
                </div>
                
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-blue-100">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-3">
                    <Zap className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">Future Ready</h3>
                  <p className="text-sm text-gray-600">Multiple Career Options</p>
                </div>
              </div>
              
              {/* Course Tags */}
              <div className="flex flex-wrap gap-3 mb-8">
                <span className="bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 px-4 py-2 rounded-full font-medium text-sm">
                  Board Exam Excellence
                </span>
                <span className="bg-gradient-to-r from-green-100 to-green-200 text-green-800 px-4 py-2 rounded-full font-medium text-sm">
                  NEET-UG Preparation
                </span>
                <span className="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 px-4 py-2 rounded-full font-medium text-sm">
                  JEE Main & Advanced
                </span>
                <span className="bg-gradient-to-r from-indigo-100 to-indigo-200 text-indigo-800 px-4 py-2 rounded-full font-medium text-sm">
                  PCM/PCB/PCMB Options
                </span>
              </div>
              
              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  to="/admission" 
                  className="bg-gradient-to-r from-purple-600 to-violet-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-purple-700 hover:to-violet-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  Start Your Journey
                </Link>
                <Link 
                  to="/contact" 
                  className="bg-white text-purple-600 px-8 py-4 rounded-xl font-semibold text-lg border-2 border-purple-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300"
                >
                  Get Expert Guidance
                </Link>
              </div>
            </div>
            
            {/* Hero Visual */}
            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <img
                    src="https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Class 11-12 Students with NCERT Books - Two Year Program"
                    className="w-full h-48 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                  <img
                    src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Educational Study Materials and Reference Books"
                    className="w-full h-40 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                </div>
                <div className="space-y-4 mt-8">
                  <img
                    src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Students Learning Physics Chemistry Biology Mathematics"
                    className="w-full h-40 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                  <img
                    src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    alt="Comprehensive Learning Environment for Two-Year Program"
                    className="w-full h-48 object-cover rounded-2xl shadow-xl hover:shadow-2xl transition-shadow duration-300"
                  />
                </div>
              </div>
              
              {/* Floating Achievement Badges */}
              <div className="absolute -top-4 -left-4 bg-white rounded-xl p-3 shadow-lg border border-purple-100">
                <div className="flex items-center">
                  <Award className="w-5 h-5 text-purple-600 mr-2" />
                  <div>
                    <p className="text-xs font-semibold text-gray-900">Expert Faculty</p>
                    <p className="text-xs text-gray-600">IIT/NIT Alumni</p>
                  </div>
                </div>
              </div>
              
              <div className="absolute -bottom-4 -right-4 bg-white rounded-xl p-3 shadow-lg border border-green-100">
                <div className="flex items-center">
                  <Users className="w-5 h-5 text-green-600 mr-2" />
                  <div>
                    <p className="text-xs font-semibold text-gray-900">Small Batches</p>
                    <p className="text-xs text-gray-600">Max 30 Students</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Bottom Stats Bar */}
          <div className="mt-16 bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-100">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-purple-600 mb-1">2 Years</div>
                <div className="text-sm font-medium text-gray-700 mb-1">Complete Program</div>
                <div className="text-xs text-gray-500">Class XI & XII</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600 mb-1">3 Streams</div>
                <div className="text-sm font-medium text-gray-700 mb-1">Subject Options</div>
                <div className="text-xs text-gray-500">PCM/PCB/PCMB</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600 mb-1">30 Max</div>
                <div className="text-sm font-medium text-gray-700 mb-1">Batch Strength</div>
                <div className="text-xs text-gray-500">Personal Attention</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-indigo-600 mb-1">100%</div>
                <div className="text-sm font-medium text-gray-700 mb-1">Syllabus Coverage</div>
                <div className="text-xs text-gray-500">Board + Competitive</div>
              </div>
            </div>
          </div>
        </div>
      </section>{/* Enhanced Stats Section with Educational Images */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Two-Year Excellence Journey</h3>
              <div className="grid grid-cols-2 gap-4 mb-8">
                {successStats.map((stat, index) => (
                  <div key={index} className="bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-xl text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">{stat.number}</div>
                    <div className="text-sm font-medium text-gray-900 mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">NCERT Foundation to Advanced</h4>
                    <p className="text-gray-600">Complete coverage from basic NCERT to competitive level</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Dual Preparation Strategy</h4>
                    <p className="text-gray-600">Simultaneous board and competitive exam preparation</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Comprehensive Study Materials</h4>
                    <p className="text-gray-600">Integrated books, notes, and practice materials</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Class 11-12 Students with NCERT Books for Two-Year Program"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Students Learning Physics Chemistry Biology Mathematics"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Educational Study Materials and Reference Books"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Comprehensive Learning Environment for Two-Year Program"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Program Features</h2>
            <p className="text-xl text-gray-600">Why choose our Two-Year Program?</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md text-center">
                <div className="bg-purple-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Syllabus Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Complete Syllabus Coverage</h2>
            <p className="text-xl text-gray-600">Comprehensive curriculum for Classes 11 & 12</p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {Object.entries(syllabusData).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-xl border border-gray-200 p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <BookOpen className="h-6 w-6 text-purple-600 mr-2" />
                  {subject}
                </h3>
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Expert Faculty Team</h2>
            <p className="text-xl text-gray-600">Learn from experienced educators with proven expertise</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md">
                <div className="text-center mb-4">
                  <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Brain className="h-10 w-10 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{faculty.name}</h3>
                  <p className="text-purple-600 font-medium">{faculty.subject}</p>
                </div>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Experience:</span> {faculty.experience}</p>
                  <p><span className="font-medium">Qualification:</span> {faculty.qualification}</p>
                  <p><span className="font-medium">Specialization:</span> {faculty.specialization}</p>
                  <p className="text-purple-600 font-medium">{faculty.achievements}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Study Material & Resources</h2>
            <p className="text-xl text-gray-600">Everything you need for comprehensive preparation</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-6">
                <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  {material.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600">{material.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Program Benefits
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <img
                src="https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="Students studying in class"
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Available Batches</h2>
            <p className="text-xl text-gray-600">Choose your stream and batch timing</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{batch.name}</h3>
                <div className="space-y-3 mb-6">
                  <p className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Duration:</span> <span className="ml-2">{batch.duration}</span>
                  </p>
                  <p className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Timing:</span> <span className="ml-2">{batch.timing}</span>
                  </p>
                  <p className="flex items-center text-gray-600">
                    <Users className="h-5 w-5 mr-2" />
                    <span className="font-medium">Batch Size:</span> <span className="ml-2">{batch.strength}</span>
                  </p>
                </div>
                <div className="mb-6">
                  <p className="font-medium text-gray-900 mb-2">Key Features:</p>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-purple-600 to-violet-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Start Your Two-Year Journey
          </h2>
          <p className="text-xl text-purple-100 mb-8">
            Join our comprehensive Two-Year Program and excel in both board exams and competitive exams
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Enroll Now
            </Link>
            <Link
              to="/contact"
              className="border border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-200"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TwoYearProgram;
