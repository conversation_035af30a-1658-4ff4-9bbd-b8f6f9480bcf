import React, { useState } from 'react';
import { MapPin, Phone, Mail, Clock, Send, MessageCircle, Star, Sparkles, Users, Award, CheckCircle, ArrowRight } from 'lucide-react';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    mobile: '',
    email: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // Reset form
    setFormData({
      name: '',
      mobile: '',
      email: '',
      message: ''
    });
    alert('Thank you for your message! We will get back to you soon.');
  };

  const contactInfo = [
    {
      icon: <MapPin className="h-6 w-6" />,
      title: "Address",
      details: "2nd Floor, A-Square Building",
      subDetails: "Infront of Integral University, Dasauli Chauraha, Kursi Road, Lucknow (U.P.) - 226026"
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Phone",
      details: "+91-9058619887",
      subDetails: "+91-9651435200"
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email",
      details: "<EMAIL>",
      subDetails: "24/7 Support"
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Office Hours",
      details: "Mon - Sat: 9:00 AM - 6:00 PM",
      subDetails: "Sunday: 10:00 AM - 4:00 PM"
    }
  ];

  const faqs = [
    {
      question: "What are the batch sizes?",
      answer: "We maintain small batch sizes of 15-20 students to ensure personalized attention."
    },
    {
      question: "Do you provide study materials?",
      answer: "Yes, we provide comprehensive study materials including books, practice sheets, and reference materials."
    },
    {
      question: "Are there any makeup classes?",
      answer: "Yes, we conduct makeup classes for students who miss regular sessions due to genuine reasons."
    },
    {
      question: "How can I track my child's progress?",
      answer: "We provide regular progress reports and conduct parent-teacher meetings to discuss student performance."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Modern Hero Section */}
      <section className="relative py-16 sm:py-20 lg:py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-50 via-white to-amber-50">
          <div className="absolute top-20 left-10 w-72 h-72 bg-violet-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-20 left-1/2 w-96 h-96 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-pulse"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-amber-100 rounded-full px-6 py-3 mb-8 border border-violet-200/50 backdrop-blur-sm shadow-lg">
              <Sparkles className="h-5 w-5 text-violet-600 mr-3 animate-pulse" />
              <span className="text-sm font-semibold text-violet-700 tracking-wide">Get in Touch with Us</span>
            </div>

            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-8 relative">
              <div className="relative inline-block">
                <span className="bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent drop-shadow-sm">
                  Contact Us
                </span>
              </div>
            </h1>

            <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed drop-shadow-sm mb-12">
              Have questions about our courses or need guidance? We're here to help!
              Get in touch with us through any of the following ways.
            </p>

            {/* Quick Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <a
                href="tel:+************"
                className="group bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center"
              >
                <Phone className="mr-3 h-6 w-6 group-hover:rotate-12 transition-transform" />
                Call Now
              </a>
              <a
                href="https://wa.me/************"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-gradient-to-r from-green-400 to-green-500 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-green-500 hover:to-green-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center"
              >
                <MessageCircle className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform" />
                WhatsApp
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
            {/* Contact Form */}
            <div className="order-2 lg:order-1">
              <div className="bg-gradient-to-br from-white to-gray-50 rounded-3xl p-8 lg:p-10 shadow-2xl border border-gray-100">
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
                  Send us a <span className="bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">Message</span>
                </h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid sm:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-3">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
                        placeholder="Enter your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="mobile" className="block text-sm font-semibold text-gray-700 mb-3">
                        Mobile Number *
                      </label>
                      <input
                        type="tel"
                        id="mobile"
                        name="mobile"
                        value={formData.mobile}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
                        placeholder="Enter your mobile number"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-3">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
                      placeholder="Enter your email address"
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-3">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md resize-none"
                      placeholder="Tell us about your requirements, questions, or how we can help you..."
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-violet-600 to-indigo-600 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-violet-700 hover:to-indigo-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center group"
                  >
                    <Send className="mr-3 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    Send Message
                  </button>
                </form>
              </div>
            </div>

            {/* Contact Information */}
            <div className="order-1 lg:order-2">
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
                    Get in <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent">Touch</span>
                  </h2>
                  <p className="text-lg text-gray-600 mb-8">
                    Ready to start your journey to success? Contact us today and let's discuss how we can help you achieve your academic goals.
                  </p>
                </div>

                <div className="grid gap-6">
                  {contactInfo.map((info, index) => (
                    <div key={index} className="group bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                      <div className="flex items-start space-x-4">
                        <div className="bg-gradient-to-r from-violet-100 to-purple-100 p-4 rounded-xl group-hover:scale-110 transition-transform duration-300">
                          <div className="text-violet-600">
                            {info.icon}
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-2">{info.title}</h3>
                          <p className="text-gray-700 font-medium mb-1">{info.details}</p>
                          <p className="text-gray-500 text-sm">{info.subDetails}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Why Choose Us */}
                <div className="bg-gradient-to-br from-violet-50 to-purple-50 rounded-2xl p-8 border border-violet-100">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Choose New Heights Classes?</h3>
                  <div className="grid gap-4">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">Expert faculty with proven track record</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">Small batch sizes for personalized attention</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">Latest NTA pattern mock tests</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">95% success rate in competitive exams</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Location & Map Section */}
      <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Visit Our <span className="bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">Campus</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Experience our state-of-the-art facilities and meet our expert faculty. Schedule a campus tour and personal consultation.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100">
            <div className="relative h-96 lg:h-[500px] bg-gradient-to-br from-violet-100 to-purple-100">
              {/* Enhanced Map Placeholder */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center p-8">
                  <div className="bg-white rounded-full p-6 shadow-lg mb-6 inline-block">
                    <MapPin className="h-12 w-12 text-violet-600" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">New Heights Classes</h3>
                  <p className="text-gray-700 font-medium mb-2">2nd Floor, A-Square Building</p>
                  <p className="text-gray-600">Infront of Integral University, Dasauli Chauraha</p>
                  <p className="text-gray-600">Kursi Road, Lucknow (U.P.) - 226026</p>
                  <div className="mt-6">
                    <button className="bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-violet-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                      Get Directions
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent">Questions</span>
            </h2>
            <p className="text-lg text-gray-600">Quick answers to common questions about our programs and services</p>
          </div>
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="group bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-violet-600 transition-colors duration-300">{faq.question}</h3>
                <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-20 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-10 w-72 h-72 bg-white/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-white/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            Still Have Questions?
          </h2>
          <p className="text-xl lg:text-2xl text-white/90 mb-10 max-w-3xl mx-auto">
            Schedule a free consultation with our academic counselors and discover how we can help you achieve your dreams.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="group bg-white text-violet-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center">
              Book Free Consultation
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </button>
            <a
              href="tel:+************"
              className="group bg-gradient-to-r from-amber-500 to-orange-500 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center"
            >
              <Phone className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
              Call Now
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;