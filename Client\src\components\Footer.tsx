import { Link } from 'react-router-dom';
import { GraduationCap, MapPin, Phone, Mail, Facebook, Instagram, Youtube, ArrowRight } from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { path: '/', label: 'Home' },
    { path: '/about', label: 'About' },
    { path: '/courses', label: 'Programs' },
    { path: '/pricing', label: 'Pricing' },
    { path: '/contact', label: 'Contact' }
  ];

  const programs = [
    { path: '/courses/pre-foundation', label: 'Pre-Foundation (IX-X)' },
    { path: '/courses/foundation', label: 'Foundation (XI-XII)' },
    { path: '/courses/dropper', label: 'Dropper\'s Batch' },
    { path: '/courses/amu', label: 'AMU Entrance' }
  ];

  const socialLinks = [
    { icon: <Facebook className="h-5 w-5" />, url: '#', name: 'Facebook' },
    { icon: <Instagram className="h-5 w-5" />, url: '#', name: 'Instagram' },
    { icon: <Youtube className="h-5 w-5" />, url: '#', name: 'YouTube' }
  ];

  return (
    <footer className="bg-slate-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16 grid md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand & Description */}
          <div className="lg:col-span-1">
            <Link to="/" className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
                <GraduationCap className="h-7 w-7 text-slate-900" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">New Heights</span>
                <div className="text-sm text-slate-400">Classes</div>
              </div>
            </Link>
            <p className="text-slate-400 mb-8 leading-relaxed">
              Transforming academic dreams into reality through innovative teaching methodologies and personalized mentorship.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-blue-500 transition-colors duration-200 group"
                  aria-label={social.name}
                >
                  <div className="group-hover:scale-110 transition-transform duration-200">
                    {social.icon}
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Quick Links</h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.path}
                    className="text-slate-400 hover:text-white transition-colors duration-200 flex items-center group"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Programs */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Academic Programs</h3>
            <ul className="space-y-3">
              {programs.map((program, index) => (
                <li key={index}>
                  <Link
                    to={program.path}
                    className="text-slate-400 hover:text-white transition-colors duration-200 flex items-center group"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">{program.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Get in Touch</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <MapPin className="h-4 w-4 text-blue-400" />
                </div>
                <div className="text-slate-400">
                  <p className="font-medium text-white mb-1">Our Location</p>
                  <p>2nd Floor, A Square Building</p>
                  <p>Near Integral Hospital, Kursi Road</p>
                  <p>Lucknow, UP – 226026</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <Phone className="h-4 w-4 text-emerald-400" />
                </div>
                <div className="text-slate-400">
                  <p className="font-medium text-white mb-1">Call Us</p>
                  <a
                    href="tel:+919651435200"
                    className="hover:text-white transition-colors duration-200 block"
                  >
                    +91-9651435200
                  </a>
                  <a
                    href="tel:+919452856268"
                    className="hover:text-white transition-colors duration-200 block"
                  >
                    +91-9452856268
                  </a>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <Mail className="h-4 w-4 text-orange-400" />
                </div>
                <div className="text-slate-400">
                  <p className="font-medium text-white mb-1">Email Us</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:text-white transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-800 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">
              © 2025 New Heights Classes. All rights reserved.
            </p>
            <div className="flex space-x-8 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors duration-200">
                Terms of Service
              </a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors duration-200">
                Refund Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;