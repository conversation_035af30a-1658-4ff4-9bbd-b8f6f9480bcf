import { Link } from 'react-router-dom';
import { GraduationCap, MapPin, Phone, Mail, Facebook, Instagram, Youtube, ArrowRight, Sparkles, Star, Heart, Zap } from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { path: '/', label: 'Home' },
    { path: '/about', label: 'About' },
    { path: '/courses', label: 'Programs' },
    { path: '/pricing', label: 'Pricing' },
    { path: '/contact', label: 'Contact' }
  ];

  const programs = [
    { path: '/courses/pre-foundation', label: 'Pre-Foundation (IX-X)' },
    { path: '/courses/foundation', label: 'Foundation (XI-XII)' },
    { path: '/courses/dropper', label: 'Dropper\'s Batch' },
    { path: '/courses/amu', label: 'AMU Entrance' }
  ];

  const socialLinks = [
    { icon: <Facebook className="h-5 w-5" />, url: '#', name: 'Facebook' },
    { icon: <Instagram className="h-5 w-5" />, url: '#', name: 'Instagram' },
    { icon: <Youtube className="h-5 w-5" />, url: '#', name: 'YouTube' }
  ];

  return (
    <footer className="bg-gradient-to-br from-slate-900 via-purple-900 to-violet-900 text-white relative overflow-hidden">
      {/* Modern Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-violet-600/10 via-purple-600/5 to-amber-600/10"></div>
        <div className="absolute top-20 left-20 w-96 h-96 bg-violet-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-amber-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Modern CTA Section */}
        <div className="py-16 border-b border-white/10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center bg-gradient-to-r from-violet-100/10 to-amber-100/10 rounded-full px-6 py-3 mb-8 border border-white/20 backdrop-blur-sm">
              <Sparkles className="h-5 w-5 text-violet-400 mr-3 animate-pulse" />
              <span className="text-sm font-semibold text-violet-300 tracking-wide">Join the Success Story</span>
            </div>

            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold font-display mb-6">
              Ready to Reach <span className="bg-gradient-to-r from-violet-400 to-amber-400 bg-clip-text text-transparent">New Heights?</span>
            </h2>

            <p className="text-lg text-slate-300 mb-10 max-w-2xl mx-auto leading-relaxed">
              Join thousands of successful students who have achieved their dreams with our proven teaching methodology and expert guidance.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/admission"
                className="group bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-violet-700 hover:to-indigo-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center"
              >
                Start Your Journey
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                to="/contact"
                className="group bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/20 transition-all duration-300 border border-white/20 hover:border-white/40 flex items-center justify-center"
              >
                <Phone className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
                Get Free Consultation
              </Link>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="py-16 grid md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand & Description */}
          <div className="lg:col-span-1">
            <Link to="/" className="flex items-center space-x-3 mb-8 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative w-14 h-14 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-2xl flex items-center justify-center">
                  <GraduationCap className="h-8 w-8 text-white" />
                </div>
              </div>
              <div>
                <span className="text-2xl font-bold font-display bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent">New Heights</span>
                <div className="text-sm text-slate-400 font-medium">Classes</div>
              </div>
            </Link>

            <p className="text-slate-300 mb-8 leading-relaxed text-lg">
              Transforming academic dreams into reality through innovative teaching methodologies and personalized mentorship.
            </p>

            <div className="flex items-center space-x-2 mb-6">
              <Star className="h-5 w-5 text-amber-400" />
              <Star className="h-5 w-5 text-amber-400" />
              <Star className="h-5 w-5 text-amber-400" />
              <Star className="h-5 w-5 text-amber-400" />
              <Star className="h-5 w-5 text-amber-400" />
              <span className="text-slate-300 ml-2 font-medium">4.9/5 Student Rating</span>
            </div>

            <div className="flex space-x-3">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-gradient-to-r hover:from-violet-600 hover:to-indigo-600 transition-all duration-300 group border border-white/20 hover:border-transparent"
                  aria-label={social.name}
                >
                  <div className="group-hover:scale-110 transition-transform duration-200">
                    {social.icon}
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold font-display mb-8 text-white flex items-center">
              <Zap className="h-5 w-5 text-violet-400 mr-2" />
              Quick Links
            </h3>
            <ul className="space-y-4">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.path}
                    className="text-slate-300 hover:text-white transition-all duration-300 flex items-center group text-lg hover:translate-x-2"
                  >
                    <div className="w-2 h-2 bg-violet-400 rounded-full mr-4 group-hover:scale-150 transition-transform duration-300"></div>
                    <span className="group-hover:text-violet-300 transition-colors duration-300">{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Programs */}
          <div>
            <h3 className="text-xl font-bold font-display mb-8 text-white flex items-center">
              <Heart className="h-5 w-5 text-amber-400 mr-2" />
              Academic Programs
            </h3>
            <ul className="space-y-4">
              {programs.map((program, index) => (
                <li key={index}>
                  <Link
                    to={program.path}
                    className="text-slate-300 hover:text-white transition-all duration-300 flex items-center group text-lg hover:translate-x-2"
                  >
                    <div className="w-2 h-2 bg-amber-400 rounded-full mr-4 group-hover:scale-150 transition-transform duration-300"></div>
                    <span className="group-hover:text-amber-300 transition-colors duration-300">{program.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold font-display mb-8 text-white flex items-center">
              <Star className="h-5 w-5 text-emerald-400 mr-2" />
              Get in Touch
            </h3>
            <div className="space-y-6">
              <div className="group">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center flex-shrink-0 border border-blue-500/30 group-hover:border-blue-400 transition-colors duration-300">
                    <MapPin className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="text-slate-300">
                    <p className="font-bold text-white mb-2 text-lg">Our Location</p>
                    <p className="leading-relaxed">2nd Floor, A-Square Building<br />
                    Infront of Integral University<br />
                    Dasauli Chauraha, Kursi Road<br />
                    Lucknow (U.P.) - 226026</p>
                  </div>
                </div>
              </div>

              <div className="group">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-500/20 to-green-500/20 rounded-xl flex items-center justify-center flex-shrink-0 border border-emerald-500/30 group-hover:border-emerald-400 transition-colors duration-300">
                    <Phone className="h-6 w-6 text-emerald-400" />
                  </div>
                  <div className="text-slate-300">
                    <p className="font-bold text-white mb-2 text-lg">Call Us</p>
                    <a
                      href="tel:+919651435200"
                      className="hover:text-emerald-300 transition-colors duration-300 block text-lg font-medium"
                    >
                      +91-9651435200
                    </a>
                    <a
                      href="tel:+919452856268"
                      className="hover:text-emerald-300 transition-colors duration-300 block text-lg font-medium"
                    >
                      +91-9452856268
                    </a>
                  </div>
                </div>
              </div>

              <div className="group">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500/20 to-amber-500/20 rounded-xl flex items-center justify-center flex-shrink-0 border border-orange-500/30 group-hover:border-orange-400 transition-colors duration-300">
                    <Mail className="h-6 w-6 text-orange-400" />
                  </div>
                  <div className="text-slate-300">
                    <p className="font-bold text-white mb-2 text-lg">Email Us</p>
                    <a
                      href="mailto:<EMAIL>"
                      className="hover:text-orange-300 transition-colors duration-300 block text-lg font-medium"
                    >
                      <EMAIL>
                    </a>
                    <a
                      href="mailto:<EMAIL>"
                      className="hover:text-orange-300 transition-colors duration-300 block text-lg font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Bottom Bar */}
        <div className="border-t border-white/10 py-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <p className="text-slate-300 text-lg font-medium">
                © 2025 New Heights Classes. All rights reserved.
              </p>
              <div className="hidden lg:flex items-center space-x-2">
                <Heart className="h-4 w-4 text-red-400" />
                <span className="text-slate-400 text-sm">Made with love in Lucknow</span>
              </div>
            </div>

            <div className="flex flex-wrap justify-center lg:justify-end gap-6">
              <a href="#" className="text-slate-400 hover:text-violet-300 text-sm font-medium transition-colors duration-300 hover:underline">
                Privacy Policy
              </a>
              <a href="#" className="text-slate-400 hover:text-violet-300 text-sm font-medium transition-colors duration-300 hover:underline">
                Terms of Service
              </a>
              <a href="#" className="text-slate-400 hover:text-violet-300 text-sm font-medium transition-colors duration-300 hover:underline">
                Refund Policy
              </a>
              <a href="#" className="text-slate-400 hover:text-violet-300 text-sm font-medium transition-colors duration-300 hover:underline">
                Student Portal
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;