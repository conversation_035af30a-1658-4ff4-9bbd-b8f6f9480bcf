import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, Users, BookOpen, Award, Target, Clock, Star, CheckCircle, Zap, TrendingUp, Sparkles, GraduationCap, Trophy } from 'lucide-react';
import HeroCarousel from '../components/HeroCarousel';
import { coursesData } from '../data/coursesData';

const Home = () => {

  const highlights = [
    {
      icon: <Users className="h-12 w-12" />,
      title: "Expert Mentors",
      description: "10+ Years Experience",
      gradient: "from-violet-500 to-purple-600"
    },
    {
      icon: <Zap className="h-12 w-12" />,
      title: "Smart Learning",
      description: "AI-Powered Practice",
      gradient: "from-amber-500 to-orange-600"
    },
    {
      icon: <TrendingUp className="h-12 w-12" />,
      title: "Proven Results",
      description: "95% Success Rate",
      gradient: "from-emerald-500 to-teal-600"
    },
    {
      icon: <Clock className="h-12 w-12" />,
      title: "Flexible Learning",
      description: "Online + Offline",
      gradient: "from-blue-500 to-indigo-600"
    }
  ];

  const whyChooseUs = [
    {
      icon: <Users className="h-8 w-8" />,
      title: "Personalized Attention",
      description: "Small batches ensure every student gets individual focus and guidance"
    },
    {
      icon: <Brain className="h-8 w-8" />,
      title: "Conceptual Mastery",
      description: "Deep understanding over rote learning with interactive teaching methods"
    },
    {
      icon: <Target className="h-8 w-8" />,
      title: "Goal-Oriented Approach",
      description: "Customized study plans aligned with each student's academic objectives"
    },
    {
      icon: <Rocket className="h-8 w-8" />,
      title: "Innovation in Education",
      description: "Latest teaching technologies and methodologies for enhanced learning"
    }
  ];

  const testimonials = [
    {
      name: "Priya Sharma",
      class: "Class 10 Graduate",
      rating: 5,
      text: "NH Classes transformed my approach to learning. The personalized attention and innovative teaching methods helped me achieve 96% in boards!",
      image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      achievement: "96% in Boards"
    },
    {
      name: "Arjun Patel",
      class: "NEET Qualifier",
      rating: 5,
      text: "The conceptual clarity and strategic preparation at NH Classes made NEET seem achievable. Forever grateful to my mentors!",
      image: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      achievement: "NEET Qualified"
    },
    {
      name: "Sneha Gupta",
      class: "JEE Success Story",
      rating: 5,
      text: "The faculty's dedication and the comprehensive study material provided the perfect foundation for my JEE success.",
      image: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      achievement: "JEE Mains 98%ile"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Modern Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-violet-50 via-white to-amber-50">
          <div className="absolute top-20 left-10 w-72 h-72 bg-violet-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-20 left-1/2 w-96 h-96 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-pulse"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center pt-20">
            {/* Left Content */}
            <div className="text-center lg:text-left relative order-2 lg:order-1">
              {/* Background accent */}
              <div className="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-r from-violet-100 to-purple-100 rounded-full blur-2xl opacity-30"></div>

              <div className="relative z-10">
                <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-amber-100 rounded-full px-6 py-3 mb-8 border border-violet-200/50 backdrop-blur-sm shadow-lg">
                  <Sparkles className="h-5 w-5 text-violet-600 mr-3 animate-pulse" />
                  <span className="text-sm font-semibold text-violet-700 tracking-wide">Pioneering Excellence Since 2025</span>
                </div>

                <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight mb-8 relative">
                  <div className="relative inline-block">
                    <span className="bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent drop-shadow-sm">
                      New Heights
                    </span>
                  </div>
                  <br />
                  <div className="relative inline-block">
                    <span className="text-gray-900 drop-shadow-sm">Classes</span>
                  </div>
                  <br />
                  <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent text-2xl sm:text-3xl lg:text-4xl xl:text-5xl drop-shadow-sm font-extrabold">
                    Where Dreams Soar High!
                  </span>
                </h1>

                <p className="text-lg lg:text-xl text-gray-600 mb-10 leading-relaxed max-w-2xl drop-shadow-sm">
                  Transform your academic journey with Lucknow's most innovative NEET & JEE coaching institute.
                  Experience personalized education designed for your success with proven results.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 mb-8">
                  <Link
                    to="/courses"
                    className="group bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-violet-700 hover:to-indigo-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center relative overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative z-10">Explore Courses</span>
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200 relative z-10" />
                  </Link>
                  <Link
                    to="/admission"
                    className="group bg-gradient-to-r from-amber-500 to-orange-500 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-amber-600 hover:to-orange-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-2 flex items-center justify-center relative overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative z-10">Start Your Journey</span>
                    <GraduationCap className="ml-2 h-5 w-5 group-hover:rotate-12 transition-transform duration-200 relative z-10" />
                  </Link>
                </div>

                <div className="flex flex-wrap items-center justify-center lg:justify-start gap-4 text-sm text-gray-600">
                  <div className="flex items-center group cursor-pointer bg-white/50 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-green-200 transition-colors duration-200">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    </div>
                    <span className="group-hover:text-green-600 transition-colors duration-200 font-medium">Admissions Open</span>
                  </div>
                  <div className="flex items-center group cursor-pointer bg-white/50 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-blue-200 transition-colors duration-200">
                      <Users className="h-3 w-3 text-blue-500" />
                    </div>
                    <span className="group-hover:text-blue-600 transition-colors duration-200 font-medium">Expert Faculty</span>
                  </div>
                  <div className="flex items-center group cursor-pointer bg-white/50 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-purple-200 transition-colors duration-200">
                      <Trophy className="h-3 w-3 text-purple-500" />
                    </div>
                    <span className="group-hover:text-purple-600 transition-colors duration-200 font-medium">95% Success Rate</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Interactive Course Carousel */}
            <div className="relative flex justify-center lg:justify-end order-1 lg:order-2">
              <div className="w-full max-w-lg h-[500px] lg:h-[600px]">
                <HeroCarousel />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Highlights */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Students Choose 
              <span className="bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent"> NH Classes</span>
            </h2>
            <p className="text-base text-gray-600 max-w-2xl mx-auto">
              Experience the future of education with our innovative approach and proven methodologies
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {highlights.map((highlight, index) => (
              <div key={index} className="group text-center">
                <div className={`w-16 h-16 bg-gradient-to-r ${highlight.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <div className="text-white">
                    {highlight.icon}
                  </div>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{highlight.title}</h3>
                <p className="text-sm text-gray-600">{highlight.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Courses Overview */}
      <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Learning Programs</h2>
            <p className="text-base text-gray-600 max-w-2xl mx-auto">
              Comprehensive courses designed to unlock your potential and achieve academic excellence
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coursesData.map((course, index) => (
              <div key={index} className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div className={`w-12 h-12 bg-gradient-to-r ${course.gradient} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <div className="text-white">
                    <course.icon className="h-6 w-6" />
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="text-xs font-medium text-gray-500 mb-1">{course.subtitle}</div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{course.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{course.description}</p>
                </div>
                
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {course.subjects.map((subject, idx) => (
                      <span key={idx} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium">
                        {subject}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-xs text-gray-500">
                    <Users className="h-3 w-3 mr-1" />
                    <span>{course.students} students</span>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-violet-600 group-hover:translate-x-1 transition-all duration-200" />
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link
              to="/courses"
              className="inline-flex items-center bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-8 py-4 rounded-full font-semibold hover:from-violet-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
            >
              View All Courses
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              The NH Classes 
              <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent"> Advantage</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover what makes us the preferred choice for ambitious students and parents
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {whyChooseUs.map((item, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-gradient-to-r from-violet-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-violet-200 group-hover:to-indigo-200 transition-colors duration-300">
                  <div className="text-violet-600">
                    {item.icon}
                  </div>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-3">{item.title}</h3>
                <p className="text-gray-600 leading-relaxed">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gradient-to-br from-violet-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Success Stories</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Real achievements from our incredible students who dared to dream big
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                    <p className="text-gray-600 text-sm">{testimonial.class}</p>
                    <div className="inline-block bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-3 py-1 rounded-full text-xs font-medium mt-1">
                      {testimonial.achievement}
                    </div>
                  </div>
                </div>
                
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-amber-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-gray-700 italic leading-relaxed">"{testimonial.text}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Future?
          </h2>
          <p className="text-xl text-violet-100 mb-8 leading-relaxed">
            Join the community of achievers at NH Classes and unlock your unlimited potential
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center bg-white text-violet-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
            >
              Book Free Demo Class
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
            </Link>
            <Link
              to="/admission"
              className="inline-flex items-center bg-gradient-to-r from-amber-500 to-orange-500 text-white px-8 py-4 rounded-full font-semibold hover:from-amber-600 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Enroll Today
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;