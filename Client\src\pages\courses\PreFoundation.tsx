import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, Users, BookOpen, CheckCircle, Award, Calendar } from 'lucide-react';

const PreFoundation = () => {
  const courseFeatures = [
    "Strong conceptual foundation using NCERT methodology and board curriculum",
    "Early introduction to competitive exam patterns (NEET/JEE foundation)",
    "Development of analytical thinking and advanced problem-solving skills",
    "Regular olympiad, NTSE, and talent search exam preparation",
    "Study habits formation, time management, and effective learning techniques",
    "Interactive learning with practical demonstrations and laboratory sessions",
    "Career counseling and stream selection guidance for Class XI",
    "Strong base preparation for seamless transition to competitive coaching"
  ];

  const syllabus = {
    "Mathematics": ["Number Systems", "Algebra", "Geometry", "Trigonometry", "Statistics", "Coordinate Geometry"],
    "Physics": ["Motion & Force", "Light", "Sound", "Electricity", "Magnetic Effects", "Natural Phenomena"],
    "Chemistry": ["Atomic Structure", "Periodic Table", "Chemical Bonding", "Acids & Bases", "Metals & Non-metals", "Organic Chemistry"],
    "Biology": ["Life Processes", "Heredity & Evolution", "Natural Resource Management", "Cell Biology", "Human Body Systems"],
    "English": ["Literature", "Grammar", "Writing Skills", "Comprehension", "Vocabulary Building"],
    "Mental Ability": ["Logical Reasoning", "Analytical Skills", "Pattern Recognition", "Problem Solving", "Critical Thinking"]
  };

  const batchDetails = [
    { timing: "Morning Batch", time: "6:00 AM - 9:00 AM", days: "Monday to Saturday" },
    { timing: "Evening Batch", time: "4:00 PM - 7:00 PM", days: "Monday to Saturday" },
    { timing: "Weekend Batch", time: "9:00 AM - 4:00 PM", days: "Saturday & Sunday" }
  ];

  const faculty = [
    { name: "Dr. Rajesh Kumar", subject: "Mathematics", experience: "15 Years", qualification: "Ph.D. from IIT Delhi" },
    { name: "Prof. Meera Sharma", subject: "Physics", experience: "12 Years", qualification: "M.Sc. Physics, Gold Medalist" },
    { name: "Dr. Amit Gupta", subject: "Chemistry", experience: "10 Years", qualification: "Ph.D. Chemistry, Research Fellow" },
    { name: "Mrs. Priya Singh", subject: "Biology", experience: "8 Years", qualification: "M.Sc. Botany, B.Ed." }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Header */}
      <section className="relative bg-gradient-to-br from-violet-600 via-purple-600 to-indigo-600 text-white py-16 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-violet-600/80 via-purple-600/80 to-indigo-600/80"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-amber-400/20 rounded-full blur-xl"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/courses" className="inline-flex items-center text-violet-100 hover:text-white mb-6 transition-colors">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Courses
          </Link>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Pre Foundation Course</h1>
              <p className="text-xl text-violet-100 mb-6">Class IX & X Foundation Years</p>
              <p className="text-lg text-violet-50 leading-relaxed mb-8">
                Build a strong academic foundation with our comprehensive Pre Foundation Course designed for Classes 9 & 10. 
                Combining NCERT mastery with early competitive preparation to ensure smooth transition to advanced studies.
              </p>
              
              <div className="flex flex-wrap gap-4 mb-8">
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Clock className="h-5 w-5 inline mr-2" />
                  2 Years Duration
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Users className="h-5 w-5 inline mr-2" />
                  150+ Students
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <BookOpen className="h-5 w-5 inline mr-2" />
                  NCERT + Foundation
                </div>
              </div>
              
              <Link
                to="/admission"
                className="bg-white text-violet-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Enroll Now
                <ArrowLeft className="h-5 w-5 ml-2 rotate-180" />
              </Link>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400"
                alt="Pre Foundation Course students studying"
                className="rounded-2xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Course Highlights</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {courseFeatures.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-emerald-500 mt-1 flex-shrink-0" />
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Syllabus */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Detailed Syllabus</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Object.entries(syllabus).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-xl font-bold text-emerald-600 mb-4">{subject}</h3>
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full mr-3"></div>
                      {topic}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Expert Faculty</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {faculty.map((teacher, index) => (
              <div key={index} className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-6 text-center">
                <div className="w-20 h-20 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{teacher.name}</h3>
                <p className="text-emerald-600 font-medium mb-1">{teacher.subject}</p>
                <p className="text-sm text-gray-600 mb-2">{teacher.experience} Experience</p>
                <p className="text-xs text-gray-500">{teacher.qualification}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Batch Timings */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Batch Timings</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center">
                <Calendar className="h-12 w-12 text-emerald-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">{batch.timing}</h3>
                <p className="text-emerald-600 font-medium mb-2">{batch.time}</p>
                <p className="text-gray-600">{batch.days}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-teal-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Your Foundation Journey?</h2>
          <p className="text-xl text-emerald-100 mb-8">
            Join our Pre Foundation Course and build the strong academic base you need for future success.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/admission"
              className="bg-white text-emerald-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Enroll Now
            </Link>
            <Link 
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-emerald-600 transition-colors"
            >
              Get More Info
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PreFoundation;
