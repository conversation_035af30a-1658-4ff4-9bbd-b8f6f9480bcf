import { ArrowLeft, BookOpen, Users, Clock, Award, CheckCircle, Star, Trophy, Target, Calendar, Phone, Cpu, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';

const JEEPreparation = () => {
  const courseHighlights = [
    "Gateway to India's premier engineering institutions",
    "IIT, NIT, IIIT, and top engineering colleges",
    "JEE Main & Advanced comprehensive preparation", 
    "Over 12 lakh students appear for JEE Main annually",
    "Two-tier examination system for maximum opportunities"
  ];

  const syllabusTopics = {
    Mathematics: [
      "Algebra & Complex Numbers",
      "Trigonometry & Coordinate Geometry",
      "Calculus & Differential Equations",
      "Vectors & 3D Geometry", 
      "Probability & Statistics",
      "Quadratic Equations & Sequences",
      "Matrices & Determinants",
      "Mathematical Reasoning & Statistics"
    ],
    Physics: [
      "Mechanics & Kinematics",
      "Thermodynamics & Kinetic Theory",
      "Waves & Sound", 
      "Electrostatics & Current Electricity",
      "Magnetic Effects & Electromagnetic Induction",
      "Optics & Wave Nature of Light",
      "Modern Physics & Atoms",
      "Dual Nature & Communication Systems"
    ],
    Chemistry: [
      "Physical Chemistry Fundamentals",
      "Atomic Structure & Chemical Bonding",
      "Thermodynamics & Chemical Equilibrium",
      "Electrochemistry & Chemical Kinetics",
      "Solid State & Solutions",
      "Organic Chemistry Basics",
      "Hydrocarbons & Functional Groups",
      "Biomolecules & Environmental Chemistry"
    ]
  };
  const facultyMembers = [
    {
      name: "Prof. Vikash Gupta",
      subject: "Mathematics",
      experience: "18+ Years",  
      qualification: "M.Sc. Mathematics, IIT Kanpur",
      specialization: "Calculus & Coordinate Geometry",
      achievements: "Mathematics Expert Teacher"
    },
    {
      name: "Dr. Sanjay Mishra", 
      subject: "Physics",
      experience: "15+ Years",
      qualification: "Ph.D. Physics, IIT Delhi",
      specialization: "Mechanics & Modern Physics", 
      achievements: "Physics Subject Specialist"
    },
    {
      name: "Prof. Neha Agarwal",
      subject: "Chemistry",
      experience: "12+ Years",
      qualification: "M.Sc. Chemistry, IIT Bombay", 
      specialization: "Physical & Organic Chemistry",
      achievements: "Chemistry Teaching Expert"
    }
  ];

  const studyMaterial = [
    {
      title: "Comprehensive Theory Notes", 
      description: "Subject-wise detailed notes covering complete JEE syllabus",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Problem Solving Techniques",
      description: "Advanced problem-solving methods and shortcut techniques",
      icon: <Settings className="h-6 w-6" />
    },
    {
      title: "Mock Test Series",
      description: "JEE Main & Advanced pattern tests with detailed analysis",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Previous Year Papers",
      description: "15+ years JEE questions with step-by-step solutions",
      icon: <Award className="h-6 w-6" />
    }
  ];
  const successStats = [
    { number: "New", label: "Institute", subtext: "Established 2025" },
    { number: "Expert", label: "Faculty", subtext: "IIT/NIT Alumni" },
    { number: "Modern", label: "Methods", subtext: "Updated Teaching" },
    { number: "Quality", label: "Coaching", subtext: "Focused Approach" }
  ];

  const batchDetails = [
    {
      name: "Foundation Batch",
      duration: "2 Years (Class 11-12)",
      timing: "Morning: 6:00 AM - 12:00 PM", 
      strength: "25 Students",
      features: ["Complete Syllabus Coverage", "Board + JEE Preparation", "Daily Practice Sessions"]
    },
    {
      name: "Target Batch",
      duration: "1 Year (Class 12/Dropper)",
      timing: "Afternoon: 1:00 PM - 7:00 PM",
      strength: "20 Students",
      features: ["Intensive Problem Solving", "Advanced Concepts", "Weekly Mock Tests"]
    },
    {
      name: "Super 30 Batch",
      duration: "1 Year (Droppers)",
      timing: "Full Day: 9:00 AM - 6:00 PM",
      strength: "30 Students", 
      features: ["IIT Focused Preparation", "Mentorship Program", "Hostel Facility Available"]
    }
  ];

  const examPattern = [
    {
      exam: "JEE Main",
      duration: "3 Hours",
      questions: "90 Questions",
      subjects: "30 each (PCM)",
      type: "Online CBT"
    },
    {
      exam: "JEE Advanced", 
      duration: "3 Hours (Each Paper)",
      questions: "54 Questions (2 Papers)",
      subjects: "18 each (PCM)",
      type: "Online CBT"
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-8">
            <Link to="/courses" className="flex items-center text-blue-600 hover:text-blue-700 font-medium">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Courses  
            </Link>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="bg-blue-600 p-3 rounded-xl mr-4">
                  <Cpu className="h-8 w-8 text-white" />
                </div>
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  Engineering Entrance
                </span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                JEE Main & Advanced
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Excel in Joint Entrance Examination for admission to India's premier engineering institutions 
                including IITs, NITs, IIITs, and other top technical colleges.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/admission"
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 text-center"
                >
                  Enroll Now
                </Link>
                <a
                  href="tel:+************"  
                  className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200 text-center flex items-center justify-center"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Call for Info
                </a>
              </div>
            </div>
              <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Engineering Students Studying JEE Mathematics and Physics"
                className="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-blue-900/50 to-transparent rounded-2xl"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <h3 className="text-2xl font-bold mb-2">JEE Success Path</h3>
                <p className="text-blue-100">Engineering Dreams Begin Here</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-6">
                {successStats.map((stat, index) => (
                  <div key={index} className="bg-white p-4 rounded-xl shadow-lg text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">{stat.number}</div>
                    <div className="font-semibold text-gray-900 text-sm mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Exam Pattern */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              JEE Exam Pattern
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Understanding the exam structure is key to effective preparation
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {examPattern.map((pattern, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">{pattern.exam}</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="font-medium text-gray-700">Duration</span>
                    <span className="text-blue-600 font-semibold">{pattern.duration}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="font-medium text-gray-700">Questions</span>
                    <span className="text-blue-600 font-semibold">{pattern.questions}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="font-medium text-gray-700">Subject Wise</span>
                    <span className="text-blue-600 font-semibold">{pattern.subjects}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="font-medium text-gray-700">Mode</span>
                    <span className="text-blue-600 font-semibold">{pattern.type}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-gradient-to-br from-amber-50 to-orange-50 p-8 rounded-2xl">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Key Highlights</h3>
            <ul className="space-y-3">
              {courseHighlights.map((highlight, index) => (
                <li key={index} className="flex items-start">
                  <Star className="h-5 w-5 text-amber-600 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{highlight}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* Syllabus Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Complete Syllabus Coverage
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive preparation for Physics, Chemistry, and Mathematics
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {Object.entries(syllabusTopics).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center mb-6">
                  <div className={`p-3 rounded-xl mr-4 ${
                    subject === 'Mathematics' ? 'bg-red-100' : 
                    subject === 'Physics' ? 'bg-blue-100' : 'bg-purple-100'
                  }`}>
                    <BookOpen className={`h-6 w-6 ${
                      subject === 'Mathematics' ? 'text-red-600' : 
                      subject === 'Physics' ? 'text-blue-600' : 'text-purple-600'
                    }`} />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{subject}</h3>
                </div>
                
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-start text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}          </div>
        </div>
      </section>

      {/* Educational Resources Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Study Materials & Resources
            </h2>
            <p className="text-xl text-gray-600">
              NCERT-based curriculum with comprehensive study materials
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-12">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">NCERT Foundation</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">NCERT Textbook Coverage</h4>
                    <p className="text-gray-600">Complete Physics, Chemistry & Mathematics NCERT books for Classes 11-12</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Concept Building</h4>
                    <p className="text-gray-600">Strong foundation through NCERT exemplars and additional practice problems</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Board + JEE Integration</h4>
                    <p className="text-gray-600">Seamless transition from board-level to JEE advanced problems</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="NCERT Physics and Chemistry Books for JEE Preparation"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Mathematics Study Materials and NCERT Books"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1434494878577-86c23bcb06b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Students Learning with Educational Books"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Educational Learning Environment for JEE Students"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl">
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Additional Study Resources</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 p-4 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-10 w-10 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Reference Books</h4>
                <p className="text-gray-600 text-sm">H.C. Verma, R.D. Sharma, O.P. Tandon and other standard references</p>
              </div>
              <div className="text-center">
                <div className="bg-green-100 p-4 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <Target className="h-10 w-10 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Practice Papers</h4>
                <p className="text-gray-600 text-sm">Extensive question banks with previous years' JEE problems</p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 p-4 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <Award className="h-10 w-10 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Mock Tests</h4>
                <p className="text-gray-600 text-sm">Regular testing with detailed performance analysis</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Expert Faculty Team
            </h2>
            <p className="text-xl text-gray-600">
              Learn from IIT alumni and experienced JEE mentors
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-6 shadow-lg text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-10 w-10 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{faculty.name}</h3>
                <div className="text-blue-600 font-semibold mb-2">{faculty.subject}</div>
                <div className="text-sm text-gray-600 mb-4">
                  <div>{faculty.qualification}</div>
                  <div>{faculty.experience}</div>
                </div>
                
                <div className="bg-white p-3 rounded-lg mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-1">Specialization</div>
                  <div className="text-sm text-gray-600">{faculty.specialization}</div>
                </div>
                
                <div className="flex items-center justify-center text-sm text-blue-600 font-semibold">
                  <Trophy className="h-4 w-4 mr-1" />
                  {faculty.achievements}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Study Material
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need for successful JEE preparation
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <div className="text-blue-600">{material.icon}</div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600 text-sm">{material.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Batches
            </h2>
            <p className="text-xl text-gray-600">
              Choose the batch that aligns with your JEE preparation goals
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className={`rounded-2xl p-6 shadow-lg ${
                batch.name === 'Super 30 Batch' 
                  ? 'bg-gradient-to-br from-yellow-50 to-amber-50 border-2 border-yellow-200' 
                  : 'bg-gradient-to-br from-blue-50 to-indigo-50'
              }`}>
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-blue-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{batch.name}</h3>
                  {batch.name === 'Super 30 Batch' && (
                    <Star className="h-5 w-5 text-yellow-500 ml-2" />
                  )}
                </div>
                
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Duration:</strong> {batch.duration}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Timing:</strong> {batch.timing}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Users className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Batch Size:</strong> {batch.strength}</span>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Link
                  to="/admission"
                  className={`w-full py-2 px-4 rounded-lg font-semibold transition-colors duration-200 text-center block ${
                    batch.name === 'Super 30 Batch'
                      ? 'bg-yellow-500 text-white hover:bg-yellow-600'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  Select This Batch
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Crack JEE?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join New Heights Classes and secure your seat in India's top engineering colleges
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Apply Now
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              Get Free Counseling
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default JEEPreparation;
