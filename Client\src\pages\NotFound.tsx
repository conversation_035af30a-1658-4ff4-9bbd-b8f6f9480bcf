import React from 'react';
import { Link } from 'react-router-dom';
import { Home, ArrowLeft } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-amber-50 flex items-center justify-center px-4 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-violet-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
      <div className="absolute top-40 right-10 w-72 h-72 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      <div className="text-center relative z-10">
        <div className="mb-8">
          <img
            src="https://images.pexels.com/photos/301920/pexels-photo-301920.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
            alt="Page not found"
            className="w-64 h-48 mx-auto rounded-2xl shadow-xl object-cover border border-violet-200/50"
          />
        </div>

        <h1 className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent mb-4 drop-shadow-sm">404</h1>
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
          Oops! Page Not Found
        </h2>
        <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
          The page you're looking for doesn't exist. It might have been moved, deleted, 
          or you entered the wrong URL.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/"
            className="inline-flex items-center bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-violet-700 hover:to-indigo-700 transition-all duration-200 group shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <Home className="mr-2 h-5 w-5" />
            Back to Home
          </Link>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-colors duration-200"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Go Back
          </button>
        </div>
        
        <div className="mt-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Pages</h3>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              to="/courses"
              className="text-violet-600 hover:text-violet-700 hover:underline transition-colors duration-200"
            >
              Our Courses
            </Link>
            <Link
              to="/about"
              className="text-violet-600 hover:text-violet-700 hover:underline transition-colors duration-200"
            >
              About Us
            </Link>
            <Link
              to="/contact"
              className="text-violet-600 hover:text-violet-700 hover:underline transition-colors duration-200"
            >
              Contact
            </Link>
            <Link
              to="/admission"
              className="text-violet-600 hover:text-violet-700 hover:underline transition-colors duration-200"
            >
              Admission
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;