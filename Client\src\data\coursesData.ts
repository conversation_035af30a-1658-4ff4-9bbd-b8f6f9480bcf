import { BookO<PERSON>, Users, Clock, Award, LucideIcon } from 'lucide-react';

export interface Course {
  id: string;
  title: string;
  subtitle: string;
  duration: string;
  pattern: string;
  subjects: string[];
  keyPoints: string[];
  icon: LucideIcon;
  color: string;
  gradient: string;
  students: string;
  route: string;
  image: string;
  description: string;
  highlights: string[];
}

export const coursesData: Course[] = [
  {
    id: 'pre-foundation',
    title: "Pre Foundation Course",
    subtitle: "Class IX-X Foundation Years",
    duration: "2 Years",
    pattern: "NCERT Foundation + Early Competitive Exposure",
    subjects: ["Mathematics", "Physics", "Chemistry", "Biology", "English", "Mental Ability"],
    keyPoints: [
      "Strong NCERT-based foundation",
      "Early competitive preparation",
      "Olympiad & NTSE training",
      "Study habits development"
    ],
    icon: BookOpen,
    color: "bg-emerald-600",
    gradient: "from-emerald-500 to-teal-600",
    students: "150+",
    route: "/courses/pre-foundation",
    image: "/images/pre-foundation.jpg",
    description: "Build a strong foundation for competitive exams with our comprehensive pre-foundation program designed for Class IX-X students.",
    highlights: ["NCERT Excellence", "Competitive Edge", "Study Skills"]
  },
  {
    id: 'foundation',
    title: "Foundation Course (XI-XII)",
    subtitle: "Board + NEET/JEE Integrated",
    duration: "2 Years",
    pattern: "Dual Focus: Board Excellence + Competitive Success",
    subjects: ["Physics", "Chemistry", "Mathematics", "Biology", "Board Exam Preparation"],
    keyPoints: [
      "Complete NEET/JEE syllabus",
      "Latest NTA pattern alignment",
      "PCM/PCB stream flexibility",
      "Board + competitive integration"
    ],
    icon: Award,
    color: "bg-purple-600",
    gradient: "from-purple-500 to-violet-600",
    students: "200+",
    route: "/courses/foundation",
    image: "/images/foundation.jpg",
    description: "Master both board exams and competitive tests with our integrated foundation program for Class XI-XII students.",
    highlights: ["Dual Success", "Expert Faculty", "Proven Results"]
  },
  {
    id: 'dropper',
    title: "Dropper's Batch",
    subtitle: "12th Pass/Repeaters Intensive",
    duration: "1 Year Intensive",
    pattern: "Result-Oriented Intensive Preparation",
    subjects: ["Physics", "Chemistry", "Mathematics", "Biology", "Test Series", "Revision"],
    keyPoints: [
      "Intensive syllabus revision",
      "Weak area improvement",
      "Daily practice sessions",
      "Psychological counseling"
    ],
    icon: Clock,
    color: "bg-rose-600",
    gradient: "from-rose-500 to-pink-600",
    students: "120+",
    route: "/courses/dropper",
    image: "/images/dropper.jpg",
    description: "Intensive one-year program designed specifically for 12th pass students aiming for NEET/JEE success.",
    highlights: ["Intensive Training", "Weak Area Focus", "Success Guarantee"]
  },
  {
    id: 'amu',
    title: "AMU Entrance Course",
    subtitle: "Class VIII + AMU IX Specialized",
    duration: "6-12 Months",
    pattern: "AMU Entrance Test + Academic Excellence",
    subjects: ["AMU Syllabus", "General Knowledge", "English", "Logical Reasoning", "Interview Skills"],
    keyPoints: [
      "Complete AMU entrance coverage",
      "English proficiency focus",
      "Interview preparation",
      "Campus guidance"
    ],
    icon: Users,
    color: "bg-indigo-600",
    gradient: "from-indigo-500 to-blue-600",
    students: "80+",
    route: "/courses/amu",
    image: "/images/amu.jpg",
    description: "Specialized preparation for AMU entrance with comprehensive coverage of syllabus and interview skills.",
    highlights: ["AMU Focused", "Interview Prep", "Campus Support"]
  }
];

export const studyFeatures = [
  {
    icon: Users,
    title: "Expert Faculty & Small Batches",
    description: "Highly qualified teachers with proven track record, maintaining optimal batch sizes for personalized attention"
  },
  {
    icon: Award,
    title: "Latest NTA Pattern Tests",
    description: "Regular mock tests following current NEET/JEE patterns with detailed analysis and performance tracking"
  },
  {
    icon: BookOpen,
    title: "NCERT-Based Study Material",
    description: "Comprehensive study material aligned with NCERT/CBSE guidelines and latest syllabus updates"
  },
  {
    icon: Clock,
    title: "Flexible Learning Options",
    description: "Multiple batch timings, doubt clearing sessions, and online support for continuous learning"
  }
];

export const benefits = [
  "Small batch sizes (15-20 students) for personalized attention",
  "Expert faculty with IIT/NIT/Medical background and proven results",
  "Latest NTA pattern mock tests and comprehensive performance analysis",
  "NCERT-based study material with advanced problem-solving techniques",
  "Individual doubt clearing sessions and mentorship programs",
  "Extensive previous year question papers and chapter-wise practice",
  "Regular parent-teacher meetings and progress tracking",
  "Career counseling, college selection, and admission guidance",
  "Flexible batch timings and make-up classes for missed sessions",
  "Online support and digital learning resources access"
];