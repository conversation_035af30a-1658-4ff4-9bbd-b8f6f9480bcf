import { <PERSON>, Target, BookO<PERSON>, Award, CheckCircle, Heart, Lightbulb, <PERSON>, Zap, Star, Rocket } from 'lucide-react';

const About = () => {
  const faculty = [
    {
      name: "Mr. <PERSON><PERSON>",
      subject: "Physics",
      experience: "8+ years",
      qualification: "<PERSON><PERSON>Sc <PERSON>, B.Ed",
      image: "https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop",
      specialization: "Mechanics & Modern Physics",
      achievements: "50+ JEE Qualifiers"
    },
    {
      name: "<PERSON><PERSON>",
      subject: "Chemistry",
      experience: "7+ years",
      qualification: "<PERSON><PERSON>, B.Ed",
      image: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop",
      specialization: "Organic & Inorganic Chemistry",
      achievements: "60+ NEET Qualifiers"
    },
    {
      name: "Mr. <PERSON>",
      subject: "Mathematics",
      experience: "9+ years",
      qualification: "<PERSON><PERSON>, B.Ed",
      image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop",
      specialization: "Calculus & Coordinate Geometry",
      achievements: "70+ JEE Success Stories"
    },
    {
      name: "Dr. Sunita Gupta",
      subject: "Biology",
      experience: "6+ years",
      qualification: "M.Sc Botany, Ph.D, B.Ed",
      image: "https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop",
      specialization: "Plant Biology & Human Physiology",
      achievements: "55+ NEET Toppers"
    }
  ];

  const methodology = [
    {
      icon: <Lightbulb className="h-8 w-8" />,
      title: "NCERT-Based Learning",
      description: "Strong foundation building through NCERT methodology with advanced competitive problem solving",
      gradient: "from-amber-500 to-orange-500"
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Interactive Teaching",
      description: "Smart board technology, practical demonstrations, and interactive doubt clearing sessions",
      gradient: "from-violet-500 to-purple-600"
    },
    {
      icon: <Target className="h-8 w-8" />,
      title: "Latest Exam Patterns",
      description: "Regular practice following current NTA guidelines and latest NEET/JEE pattern changes",
      gradient: "from-emerald-500 to-teal-600"
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Mentorship Program",
      description: "Personal mentoring, career counseling, and continuous motivation for student success",
      gradient: "from-rose-500 to-pink-600"
    }
  ];

  const achievements = [
    { number: "500+", label: "Students Successfully Trained", icon: <Users className="h-6 w-6" /> },
    { number: "95%", label: "Board Exam Success Rate", icon: <Trophy className="h-6 w-6" /> },
    { number: "100+", label: "NEET Qualifiers (2022-24)", icon: <Award className="h-6 w-6" /> },
    { number: "75+", label: "JEE Qualifiers (2022-24)", icon: <Target className="h-6 w-6" /> },
    { number: "8+", label: "Years of Excellence", icon: <BookOpen className="h-6 w-6" /> },
    { number: "Top 3", label: "Coaching Institute in Region", icon: <CheckCircle className="h-6 w-6" /> }
  ];

  const values = [
    {
      title: "Excellence",
      description: "Striving for the highest standards in education and student outcomes",
      icon: <Trophy className="h-12 w-12" />,
      gradient: "from-amber-500 to-orange-500"
    },
    {
      title: "Innovation",
      description: "Embracing cutting-edge teaching methods and educational technology",
      icon: <Lightbulb className="h-12 w-12" />,
      gradient: "from-violet-500 to-purple-600"
    },
    {
      title: "Integrity",
      description: "Maintaining transparency, honesty, and ethical practices in all we do",
      icon: <Heart className="h-12 w-12" />,
      gradient: "from-emerald-500 to-teal-600"
    }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-50 via-white to-amber-50">
          <div className="absolute top-20 left-10 w-72 h-72 bg-violet-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-amber-100 rounded-full px-4 py-2 mb-6 border border-violet-200/50 backdrop-blur-sm">
              <Star className="h-4 w-4 text-violet-600 mr-2 animate-pulse" />
              <span className="text-sm font-medium text-violet-700">Excellence in Education Since 2025</span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold mb-6 relative">
              <span className="text-gray-900 drop-shadow-sm">About </span>
              <div className="relative inline-block">
                <span className="bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent drop-shadow-sm">New Heights Classes</span>
                <div className="absolute -top-4 -right-8 text-violet-500 opacity-80">
                  <Rocket className="h-8 w-8 lg:h-10 lg:w-10 transform rotate-30 drop-shadow-md animate-bounce" />
                </div>
              </div>
            </h1>

            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed drop-shadow-sm">
              Empowering Future Leaders through comprehensive NEET-UG and JEE Main/Advanced preparation.
              New Heights Classes offers expert coaching from Pre-Foundation (Class 8th-9th) to Dropper's Batch,
              with specialized programs following latest NTA guidelines and NCERT-based curriculum for guaranteed success.
            </p>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="relative">
              <img
                src="https://images.pexels.com/photos/8500420/pexels-photo-8500420.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="Students studying NCERT books in modern coaching institute classroom"
                className="rounded-3xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-2xl shadow-xl">
                <div className="text-center">
                  <div className="text-3xl font-bold text-violet-600">New</div>
                  <div className="text-sm text-gray-600">Institute 2025</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-12">
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Our Vision</h2>
                </div>
                <p className="text-lg text-gray-600 leading-relaxed">
                  To be the most innovative and trusted educational institution in India, 
                  recognized globally for our excellence in nurturing future leaders, 
                  innovators, and change-makers who will shape tomorrow's world.
                </p>
              </div>
              
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center mr-4">
                    <Award className="h-6 w-6 text-white" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Our Mission</h2>
                </div>
                <p className="text-lg text-gray-600 leading-relaxed">
                  To deliver transformative education through innovative teaching methodologies, 
                  personalized learning experiences, and holistic development programs that 
                  empower every student to achieve their highest potential and succeed in 
                  their chosen path.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Core Values</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do and shape our educational philosophy
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 text-center">
                <div className={`w-20 h-20 bg-gradient-to-r ${value.gradient} rounded-3xl flex items-center justify-center mx-auto mb-6`}>
                  <div className="text-white">
                    {value.icon}
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our 
              <span className="bg-gradient-to-r from-emerald-500 to-teal-600 bg-clip-text text-transparent">Achievements</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Numbers that reflect our commitment to excellence and student success
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="bg-gradient-to-br from-violet-50 to-indigo-50 rounded-3xl p-8 text-center hover:shadow-lg transition-shadow duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <div className="text-white">
                    {achievement.icon}
                  </div>
                </div>
                <div className="text-4xl font-bold text-violet-600 mb-2">{achievement.number}</div>
                <p className="text-gray-700 font-medium">{achievement.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Meet Our Faculty */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Meet Our 
              <span className="bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent">Expert Faculty</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              World-class educators with proven expertise and passion for student success
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {faculty.map((teacher, index) => (
              <div key={index} className="bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-center">
                  <img
                    src={teacher.image}
                    alt={teacher.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-4 border-violet-100"
                  />
                  <h3 className="text-xl font-bold text-gray-900 mb-1">{teacher.name}</h3>
                  <p className="text-violet-600 font-semibold mb-2">{teacher.subject}</p>
                  <p className="text-gray-600 text-sm mb-2">{teacher.experience}</p>
                  <p className="text-gray-500 text-sm mb-3">{teacher.qualification}</p>
                  
                  <div className="bg-gradient-to-r from-violet-50 to-indigo-50 rounded-2xl p-3 mb-3">
                    <p className="text-sm font-medium text-violet-700">{teacher.specialization}</p>
                  </div>
                  
                  <div className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-full px-3 py-1 text-xs font-medium">
                    {teacher.achievements}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Teaching Methodology */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Teaching 
              <span className="bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">Methodology</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Innovative approaches that ensure effective learning, retention, and application
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {methodology.map((method, index) => (
              <div key={index} className="text-center group">
                <div className={`w-20 h-20 bg-gradient-to-r ${method.gradient} rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <div className="text-white">
                    {method.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{method.title}</h3>
                <p className="text-gray-600 leading-relaxed">{method.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Join Our Success Story
          </h2>
          <p className="text-xl text-violet-100 mb-8">
            Experience the difference that quality education and dedicated mentorship makes
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-violet-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Schedule a Visit
            </a>
            <a
              href="/admission"
              className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-8 py-4 rounded-full font-semibold hover:from-amber-600 hover:to-orange-600 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Apply Now
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;