import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, Users, BookOpen, CheckCircle, Target, Award, MessageCircle } from 'lucide-react';

const AMUEntrance = () => {
  const courseFeatures = [
    "Complete AMU entrance syllabus coverage for all target courses (Engineering, Medical, Arts)",
    "Specialized preparation for Class VIII admission and AMU Class IX entrance",
    "AMU-specific question pattern analysis and extensive previous year paper practice",
    "English proficiency development and comprehensive comprehension skills",
    "Interview preparation and personality development for courses requiring personal interviews",
    "AMU campus orientation, course information, and admission procedure guidance",
    "Subject-wise expert faculty with proven AMU entrance success record",
    "Regular mock tests simulating actual AMU entrance examination conditions"
  ];

  const courses = [
    {
      course: "Engineering",
      subjects: ["Mathematics", "Physics", "Chemistry", "General Knowledge"],
      duration: "12 Months",
      eligibility: "12th Pass (PCM)"
    },
    {
      course: "Medical",
      subjects: ["Physics", "Chemistry", "Biology", "General Knowledge"],
      duration: "12 Months",
      eligibility: "12th Pass (PCB)"
    },
    {
      course: "Class VIII",
      subjects: ["Mathematics", "Science", "English", "General Knowledge"],
      duration: "6 Months",
      eligibility: "Class VII Pass"
    },
    {
      course: "Class IX",
      subjects: ["Mathematics", "Science", "English", "Social Studies", "General Knowledge"],
      duration: "8 Months",
      eligibility: "Class VIII Pass"
    }
  ];

  const examPattern = [
    { section: "Objective Questions", marks: "75-100", time: "2-3 Hours", description: "Multiple choice questions" },
    { section: "English Proficiency", marks: "25-30", time: "30 Min", description: "Grammar, vocabulary, comprehension" },
    { section: "General Knowledge", marks: "20-25", time: "20 Min", description: "Current affairs, general awareness" },
    { section: "Subject Specific", marks: "50-60", time: "1 Hour", description: "Based on course applied for" }
  ];

  const successStories = [
    { name: "Ahmed Khan", course: "B.Tech Civil", rank: "Rank 23", year: "2024" },
    { name: "Fatima Sheikh", course: "MBBS", rank: "Rank 18", year: "2024" },
    { name: "Mohammad Ali", course: "Class IX", rank: "Rank 12", year: "2024" },
    { name: "Aisha Begum", course: "BA English", rank: "Rank 31", year: "2023" }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Header */}
      <section className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/courses" className="inline-flex items-center text-indigo-100 hover:text-white mb-6 transition-colors">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Courses
          </Link>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">AMU Entrance Course</h1>
              <p className="text-xl text-indigo-100 mb-6">Class VIII + AMU IX - Specialized Preparation</p>
              <p className="text-lg text-indigo-50 leading-relaxed mb-8">
                Comprehensive preparation for Aligarh Muslim University entrance examinations across all levels. 
                From Class VIII admission to undergraduate courses, we provide specialized coaching with proven results.
              </p>
              
              <div className="flex flex-wrap gap-4 mb-8">
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Clock className="h-5 w-5 inline mr-2" />
                  6-12 Months
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Users className="h-5 w-5 inline mr-2" />
                  80+ Students
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Target className="h-5 w-5 inline mr-2" />
                  AMU Specific
                </div>
              </div>
              
              <Link 
                to="/admission" 
                className="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
              >
                Enroll Now
                <ArrowLeft className="h-5 w-5 ml-2 rotate-180" />
              </Link>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400"
                alt="AMU entrance preparation students"
                className="rounded-2xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Course Highlights</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {courseFeatures.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-indigo-500 mt-1 flex-shrink-0" />
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Available Courses */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Available Course Preparations</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {courses.map((course, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                <BookOpen className="h-12 w-12 text-indigo-600 mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">{course.course}</h3>
                <p className="text-sm text-gray-500 mb-4">{course.eligibility}</p>
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Subjects Covered:</p>
                  <div className="flex flex-wrap gap-1">
                    {course.subjects.map((subject, idx) => (
                      <span key={idx} className="bg-indigo-100 text-indigo-700 px-2 py-1 rounded text-xs">
                        {subject}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-indigo-600 font-medium">{course.duration}</span>
                  <Target className="h-5 w-5 text-indigo-400" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Exam Pattern */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">AMU Exam Pattern</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {examPattern.map((pattern, index) => (
              <div key={index} className="bg-indigo-50 rounded-xl p-6 text-center">
                <h3 className="text-lg font-bold text-indigo-700 mb-2">{pattern.section}</h3>
                <div className="text-2xl font-bold text-indigo-600 mb-2">{pattern.marks}</div>
                <div className="text-gray-600 mb-2">{pattern.time}</div>
                <p className="text-sm text-gray-500">{pattern.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Success Stories</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {successStories.map((story, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center">
                <Award className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">{story.name}</h3>
                <p className="text-indigo-600 font-medium mb-1">{story.course}</p>
                <p className="text-gray-600 mb-2">{story.rank}</p>
                <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">{story.year}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Special Features */}
      <section className="py-16 bg-indigo-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose Our AMU Preparation?</h2>
            <p className="text-xl text-indigo-100">Specialized features that give you the edge</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <MessageCircle className="h-16 w-16 mx-auto mb-4 text-indigo-200" />
              <h3 className="text-xl font-bold mb-3">Interview Preparation</h3>
              <p className="text-indigo-100">Mock interviews, personality development, and confidence building sessions</p>
            </div>
            <div className="text-center">
              <BookOpen className="h-16 w-16 mx-auto mb-4 text-indigo-200" />
              <h3 className="text-xl font-bold mb-3">English Proficiency</h3>
              <p className="text-indigo-100">Special focus on English language skills, comprehension, and vocabulary</p>
            </div>
            <div className="text-center">
              <Target className="h-16 w-16 mx-auto mb-4 text-indigo-200" />
              <h3 className="text-xl font-bold mb-3">AMU Campus Connect</h3>
              <p className="text-indigo-100">Direct guidance from AMU alumni and campus visit arrangements</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-indigo-600 to-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Join AMU?</h2>
          <p className="text-xl text-indigo-100 mb-8">
            Start your journey to Aligarh Muslim University with our specialized preparation program.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/admission"
              className="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Enroll Now
            </Link>
            <Link 
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors"
            >
              Get AMU Guidance
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AMUEntrance;
