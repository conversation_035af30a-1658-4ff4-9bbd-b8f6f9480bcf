import { Book<PERSON>pen, Users, Clock, Target, CheckCircle, Brain, Zap, TrendingUp } from 'lucide-react';
import { Link } from 'react-router-dom';

const OneYearIntensive = () => {
  const syllabusData = {
    "Physics": [
      "Mechanics: Motion, Forces, Work & Energy",
      "Thermodynamics & Kinetic Theory",
      "Oscillations & Waves",
      "Electrostatics & Current Electricity",
      "Magnetism & Electromagnetic Induction",
      "Optics: Ray & Wave Optics",
      "Modern Physics: Atoms, Nuclei, Electronics"
    ],
    "Chemistry": [
      "Physical Chemistry: States of Matter, Thermodynamics",
      "Atomic Structure & Chemical Bonding",
      "Chemical Equilibrium & Kinetics",
      "Electrochemistry & Solutions",
      "Organic Chemistry: Hydrocarbons, Functional Groups",
      "Aldehydes, Ketones, Carboxylic Acids",
      "Biomolecules & Polymers",
      "Inorganic Chemistry: p, d & f Block Elements"
    ],
    "Mathematics": [
      "Algebra: Complex Numbers, Quadratic Equations",
      "Trigonometry & Inverse Trigonometric Functions",
      "Matrices & Determinants",
      "Calculus: Limits, Derivatives, Integrals",
      "Differential Equations",
      "Coordinate Geometry: Straight Lines, Circles, Conics",
      "Vector Algebra & 3D Geometry",
      "Statistics & Probability"
    ],
    "Biology": [
      "Diversity in Living World",
      "Cell Structure & Function",
      "Plant Physiology: Photosynthesis, Respiration",
      "Human Physiology: Digestion, Breathing, Circulation",
      "Reproduction in Organisms",
      "Genetics & Evolution",
      "Biology & Human Welfare",
      "Biotechnology & Ecology"
    ]
  };

  const facultyMembers = [
    {
      name: "Dr. Arun Kumar",
      subject: "Physics",
      experience: "16+ Years",
      qualification: "Ph.D. Physics, IIT Delhi",
      specialization: "Advanced Physics & Problem Solving",
      achievements: "Physics Expert for Droppers"
    },
    {
      name: "Prof. Sunita Agarwal",
      subject: "Chemistry", 
      experience: "19+ Years",
      qualification: "M.Sc. Chemistry, IIT Kanpur",
      specialization: "Organic & Physical Chemistry",
      achievements: "Chemistry Subject Specialist"
    },
    {
      name: "Dr. Manoj Singh",
      subject: "Mathematics",
      experience: "22+ Years", 
      qualification: "Ph.D. Mathematics, IIT Bombay",
      specialization: "Advanced Mathematics & JEE Pattern",
      achievements: "Mathematics Teaching Expert"
    },
    {
      name: "Dr. Kavita Sharma",
      subject: "Biology",
      experience: "15+ Years",
      qualification: "Ph.D. Biology, AIIMS Delhi",
      specialization: "Human Physiology & Genetics",
      achievements: "NEET Biology Specialist"
    }
  ];

  const studyMaterial = [
    {
      title: "Intensive Study Modules",
      description: "Compressed yet comprehensive modules covering complete syllabus",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Advanced Problem Sets",
      description: "High-level problems and previous year questions for thorough practice",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Speed & Accuracy Tests",
      description: "Time-bound tests to improve speed and accuracy for competitive exams",
      icon: <Zap className="h-6 w-6" />
    },
    {
      title: "Revision & Quick Notes",
      description: "Concise revision materials and formula sheets for last-minute preparation",
      icon: <TrendingUp className="h-6 w-6" />
    }
  ];

  const successStats = [
    { number: "New", label: "Institute", subtext: "Established 2025" },
    { number: "Expert", label: "Faculty", subtext: "20+ Years Experience" },
    { number: "Intensive", label: "Coaching", subtext: "Focused Approach" },
    { number: "Quality", label: "Results", subtext: "Dedicated Training" }
  ];

  const batchDetails = [
    {
      name: "NEET Intensive Batch",
      duration: "1 Year (April to March)",
      timing: "Morning: 6:00 AM - 1:00 PM",
      strength: "25 Students",
      features: ["Physics, Chemistry, Biology", "Medical Entrance Focus", "NEET Pattern Tests"]
    },
    {
      name: "JEE Intensive Batch", 
      duration: "1 Year (April to March)",
      timing: "Afternoon: 2:00 PM - 9:00 PM",
      strength: "25 Students",
      features: ["Physics, Chemistry, Mathematics", "Engineering Entrance Focus", "JEE Main & Advanced Pattern"]
    },
    {
      name: "Foundation Intensive",
      duration: "1 Year (June to May)",
      timing: "Evening: 4:00 PM - 10:00 PM", 
      strength: "20 Students",
      features: ["All Subjects Covered", "Concept Building Focus", "Multiple Exam Preparation"]
    }
  ];

  const features = [
    {
      icon: <Clock className="h-8 w-8 text-rose-600" />,
      title: "Intensive Schedule",
      description: "Fast-paced curriculum covering complete syllabus in focused timeframe"
    },
    {
      icon: <Brain className="h-8 w-8 text-rose-600" />,
      title: "Advanced Concepts",
      description: "Deep dive into complex topics with advanced problem-solving techniques"
    },
    {
      icon: <Target className="h-8 w-8 text-rose-600" />,
      title: "Exam-Focused Training",
      description: "Specialized coaching targeting NEET-UG and JEE Main/Advanced patterns"
    },
    {
      icon: <Users className="h-8 w-8 text-rose-600" />,
      title: "Small Batches",
      description: "Limited students per batch ensuring maximum individual attention"
    }
  ];

  const benefits = [
    "Complete syllabus coverage in one year",
    "Intensive coaching for serious aspirants",
    "Advanced problem-solving techniques",
    "Regular mock tests and performance analysis",
    "Individual doubt clearing sessions",
    "Comprehensive study material and notes",
    "Previous years' question papers practice",
    "Time management and exam strategy",
    "Career guidance and counseling",
    "Focused preparation for competitive exams"
  ];

  const programPhases = [
    {
      phase: "Phase 1: Foundation (Months 1-3)",
      description: "Building strong conceptual foundation and clearing basics",
      features: ["Concept clarity", "Basic problem solving", "Regular assessments"]
    },
    {
      phase: "Phase 2: Advancement (Months 4-8)",
      description: "Advanced topics and complex problem-solving techniques",
      features: ["Advanced concepts", "Challenging problems", "Speed building"]
    },
    {
      phase: "Phase 3: Mastery (Months 9-12)",
      description: "Exam pattern practice and final preparation",
      features: ["Mock tests", "Revision", "Exam strategies"]
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-rose-50 to-pink-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              One-Year Intensive Program
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
              Intensive one-year preparation program for 12th pass students and droppers targeting NEET-UG and IIT-JEE examinations
            </p>
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <span className="bg-rose-100 text-rose-800 px-4 py-2 rounded-full font-medium">12th Pass/Droppers</span>
              <span className="bg-green-100 text-green-800 px-4 py-2 rounded-full font-medium">NEET Intensive</span>
              <span className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-medium">JEE Intensive</span>
            </div>
          </div>
        </div>
      </section>      {/* Enhanced Stats Section with Educational Images */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Intensive Year of Success</h3>
              <div className="grid grid-cols-2 gap-4 mb-8">
                {successStats.map((stat, index) => (
                  <div key={index} className="bg-gradient-to-br from-rose-50 to-pink-50 p-4 rounded-xl text-center">
                    <div className="text-2xl font-bold text-rose-600 mb-1">{stat.number}</div>
                    <div className="text-sm font-medium text-gray-900 mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Accelerated NCERT Mastery</h4>
                    <p className="text-gray-600">Rapid coverage of NCERT concepts with competitive depth</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Intensive Problem Solving</h4>
                    <p className="text-gray-600">High-intensity practice sessions with time-bound solutions</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Comprehensive Study Resources</h4>
                    <p className="text-gray-600">Complete material package for one-year success</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Intensive Study with NCERT Books for Competitive Exams"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Students Focused on One-Year Intensive Preparation"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1456513080510-7bf3a84b82d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Comprehensive Study Materials for Droppers and 12th Pass"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Educational Excellence in One Year Intensive Program"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Program Features</h2>
            <p className="text-xl text-gray-600">Why choose our One-Year Intensive Program?</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md text-center">
                <div className="bg-rose-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Program Phases */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Program Structure</h2>
            <p className="text-xl text-gray-600">Systematic approach for maximum effectiveness</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {programPhases.map((phase, index) => (
              <div key={index} className="bg-gradient-to-br from-rose-50 to-pink-50 rounded-xl p-6 border border-rose-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{phase.phase}</h3>
                <p className="text-gray-600 mb-4">{phase.description}</p>
                <ul className="space-y-2">
                  {phase.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-rose-500 mr-2" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Syllabus Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Intensive Syllabus Coverage</h2>
            <p className="text-xl text-gray-600">Complete curriculum designed for competitive exam success</p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {Object.entries(syllabusData).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-xl border border-gray-200 p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <BookOpen className="h-6 w-6 text-rose-600 mr-2" />
                  {subject}
                </h3>
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Dedicated Faculty Team</h2>
            <p className="text-xl text-gray-600">Expert educators specializing in intensive coaching</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md">
                <div className="text-center mb-4">
                  <div className="w-20 h-20 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Brain className="h-10 w-10 text-rose-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{faculty.name}</h3>
                  <p className="text-rose-600 font-medium">{faculty.subject}</p>
                </div>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Experience:</span> {faculty.experience}</p>
                  <p><span className="font-medium">Qualification:</span> {faculty.qualification}</p>
                  <p><span className="font-medium">Specialization:</span> {faculty.specialization}</p>
                  <p className="text-rose-600 font-medium">{faculty.achievements}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Intensive Study Materials</h2>
            <p className="text-xl text-gray-600">Specialized resources for accelerated learning</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md">
                <div className="bg-rose-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  {material.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600">{material.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Program Benefits
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <img
                src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="Intensive study session"
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Available Batches</h2>
            <p className="text-xl text-gray-600">Choose your specialization and batch timing</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{batch.name}</h3>
                <div className="space-y-3 mb-6">
                  <p className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Duration:</span> <span className="ml-2">{batch.duration}</span>
                  </p>
                  <p className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Timing:</span> <span className="ml-2">{batch.timing}</span>
                  </p>
                  <p className="flex items-center text-gray-600">
                    <Users className="h-5 w-5 mr-2" />
                    <span className="font-medium">Batch Size:</span> <span className="ml-2">{batch.strength}</span>
                  </p>
                </div>
                <div className="mb-6">
                  <p className="font-medium text-gray-900 mb-2">Key Features:</p>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-rose-600 to-pink-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Begin Your Intensive Journey
          </h2>
          <p className="text-xl text-rose-100 mb-8">
            Join our One-Year Intensive Program and maximize your potential for competitive exam success
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-rose-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Enroll Now
            </Link>
            <Link
              to="/contact"
              className="border border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-rose-600 transition-colors duration-200"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default OneYearIntensive;
