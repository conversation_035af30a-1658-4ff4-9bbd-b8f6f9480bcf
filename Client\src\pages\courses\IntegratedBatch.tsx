import { BookOpen, Users, Clock, Target, Award, Check<PERSON>ircle, <PERSON> } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';

const IntegratedBatch = () => {
  const syllabusData = {
    Science: [
      "Physics: Motion, Force, Energy, Light, Sound",
      "Chemistry: Metals & Non-metals, Acids & Bases, Carbon compounds",
      "Biology: Life Processes, Reproduction, Heredity, Natural Resources"
    ],
    Mathematics: [
      "Number Systems & Real Numbers",
      "Polynomials & Linear Equations",
      "Coordinate Geometry",
      "Trigonometry Introduction",
      "Statistics & Probability"
    ],
    English: [
      "Reading Comprehension",
      "Grammar & Vocabulary",
      "Writing Skills",
      "Literature Analysis",
      "Communication Skills"
    ],
    "Competitive Foundation": [
      "NTSE Pattern Questions",
      "Mental Ability Tests",
      "Logical Reasoning",
      "General Knowledge",
      "Current Affairs"
    ]
  };

  const facultyMembers = [
    {
      name: "Mrs. <PERSON>",
      subject: "Science",
      experience: "14+ Years",
      qualification: "M.Sc. Physics, B.Ed.",
      specialization: "Foundation Science & NTSE Preparation",
      achievements: "Science Subject Expert"
    },
    {
      name: "Prof. <PERSON><PERSON>",
      subject: "Mathematics", 
      experience: "16+ Years",
      qualification: "M.Sc. Mathematics, B.Ed.",
      specialization: "Foundation Mathematics & Competitive Prep",
      achievements: "Mathematics Teaching Specialist"
    },
    {
      name: "Ms. Neha Singh",
      subject: "English",
      experience: "12+ Years",
      qualification: "M.A. English, B.Ed.",
      specialization: "English Language & Communication",
      achievements: "English Language Expert"
    }
  ];

  const studyMaterial = [
    {
      title: "NCERT Based Study Material",
      description: "Complete notes following NCERT textbooks with additional practice questions",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "NTSE Preparation Kit",
      description: "Specialized material for NTSE exam preparation with MAT and SAT practice",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Mock Test Series",
      description: "Regular tests to assess preparation and improve performance",
      icon: <Award className="h-6 w-6" />
    },
    {
      title: "Competitive Foundation",
      description: "Building blocks for future NEET/JEE preparation",
      icon: <Brain className="h-6 w-6" />
    }
  ];

  const successStats = [
    { number: "New", label: "Institute", subtext: "Established 2025" },
    { number: "Expert", label: "Faculty", subtext: "Experienced Team" },
    { number: "Quality", label: "Teaching", subtext: "Modern Methods" },
    { number: "100%", label: "Attention", subtext: "Individual Focus" }
  ];

  const batchDetails = [
    {
      name: "Morning Batch",
      duration: "Full Academic Year",
      timing: "7:00 AM - 12:00 PM",
      strength: "25 Students",
      features: ["Complete Syllabus Coverage", "Board Exam Focus", "NTSE Preparation"]
    },
    {
      name: "Evening Batch",
      duration: "Full Academic Year",
      timing: "2:00 PM - 7:00 PM",
      strength: "25 Students",
      features: ["Interactive Learning", "Doubt Clearing", "Regular Assessments"]
    }  ];

  const features = [
    {
      icon: <Users className="h-8 w-8 text-blue-600" />,
      title: "Small Batch Size",
      description: "Maximum 25 students per batch for personalized attention"
    },
    {
      icon: <BookOpen className="h-8 w-8 text-blue-600" />,
      title: "Comprehensive Curriculum",
      description: "Complete Class 10 syllabus with competitive exam foundation"
    },
    {
      icon: <Target className="h-8 w-8 text-blue-600" />,
      title: "NTSE Preparation",
      description: "Specialized coaching for National Talent Search Examination"
    },
    {
      icon: <Award className="h-8 w-8 text-blue-600" />,
      title: "Regular Assessments",
      description: "Monthly tests and continuous evaluation system"
    }
  ];

  const benefits = [
    "Strong foundation in Science and Mathematics",
    "Excellent board exam preparation",
    "NTSE scholarship exam coaching",
    "Development of analytical thinking",
    "Regular parent-teacher interaction",
    "Individual attention and doubt clearing",
    "Study material and practice papers",
    "Career guidance for stream selection"
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Integrated Batch - Class X
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
              Comprehensive preparation for Class 10 board exams with integrated foundation building for future competitive exams like NEET and JEE
            </p>
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <span className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full font-medium">Board Exam Preparation</span>
              <span className="bg-green-100 text-green-800 px-4 py-2 rounded-full font-medium">NTSE Coaching</span>
              <span className="bg-purple-100 text-purple-800 px-4 py-2 rounded-full font-medium">Competitive Foundation</span>
            </div>
          </div>
        </div>
      </section>      {/* Enhanced Stats Section with Educational Images */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Foundation Building Excellence</h3>
              <div className="grid grid-cols-2 gap-4 mb-8">
                {successStats.map((stat, index) => (
                  <div key={index} className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">{stat.number}</div>
                    <div className="text-sm font-medium text-gray-900 mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">NCERT-Based Learning</h4>
                    <p className="text-gray-600">Complete foundation with NCERT textbooks for all subjects</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Competitive Preparation</h4>
                    <p className="text-gray-600">Early foundation for NEET, JEE, and other entrance exams</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Class 10 Students with NCERT Mathematics and Science Books"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Students Learning in Integrated Educational Environment"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1456513080510-7bf3a84b82d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Educational Books and Study Materials for Foundation Building"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1434494878577-86c23bcb06b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Academic Excellence and Learning Environment"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Course Features</h2>
            <p className="text-xl text-gray-600">Why choose our Integrated Batch program?</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md text-center">
                <div className="bg-blue-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Syllabus Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Comprehensive Syllabus</h2>
            <p className="text-xl text-gray-600">Complete Class 10 curriculum with competitive exam foundation</p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {Object.entries(syllabusData).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-xl border border-gray-200 p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <BookOpen className="h-6 w-6 text-blue-600 mr-2" />
                  {subject}
                </h3>
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Expert Faculty</h2>
            <p className="text-xl text-gray-600">Learn from experienced educators dedicated to your success</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md">
                <div className="text-center mb-4">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-10 w-10 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{faculty.name}</h3>
                  <p className="text-blue-600 font-medium">{faculty.subject}</p>
                </div>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Experience:</span> {faculty.experience}</p>
                  <p><span className="font-medium">Qualification:</span> {faculty.qualification}</p>
                  <p><span className="font-medium">Specialization:</span> {faculty.specialization}</p>
                  <p className="text-blue-600 font-medium">{faculty.achievements}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Study Material & Resources</h2>
            <p className="text-xl text-gray-600">Comprehensive learning resources for complete preparation</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-6">
                <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  {material.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600">{material.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Program Benefits
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <img
                src="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="Students in classroom"
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Batch Information</h2>
            <p className="text-xl text-gray-600">Choose the batch timing that suits you best</p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{batch.name}</h3>
                <div className="space-y-3 mb-6">
                  <p className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Duration:</span> <span className="ml-2">{batch.duration}</span>
                  </p>
                  <p className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Timing:</span> <span className="ml-2">{batch.timing}</span>
                  </p>
                  <p className="flex items-center text-gray-600">
                    <Users className="h-5 w-5 mr-2" />
                    <span className="font-medium">Batch Size:</span> <span className="ml-2">{batch.strength}</span>
                  </p>
                </div>
                <div className="mb-6">
                  <p className="font-medium text-gray-900 mb-2">Key Features:</p>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join our Integrated Batch for Class 10 and build a strong foundation for your academic future
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Enroll Now
            </Link>
            <Link
              to="/contact"
              className="border border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default IntegratedBatch;
