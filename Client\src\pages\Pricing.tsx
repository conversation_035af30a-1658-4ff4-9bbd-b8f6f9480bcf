import { CheckCircle, CreditCard, Smartphone, Banknote, Gift, Star, Rocket, DollarSign } from 'lucide-react';

const Pricing = () => {
  const pricingData = [
    {
      class: "Pre-Foundation",
      subtitle: "Class IX & X",
      duration: "Per Annum",
      originalFee: "₹40,000",
      discount: "₹8,000",
      feeAfterDiscount: "₹32,000",
      oneTimeFee: "₹32,000",
      installments: {
        first: "₹20,000",
        second: "₹12,000 (Oct)"
      },
      features: [
        "Integrated batch for Classes 9 & 10",
        "Strong conceptual foundation",
        "NCERT-based curriculum",
        "Math, Science & English focus",
        "Study material included"
      ],
      badge: "Foundation",
      badgeColor: "bg-blue-500"
    },
    {
      class: "Foundation",
      subtitle: "Class XI & XII",
      duration: "Per Annum",
      originalFee: "₹50,000",
      discount: "₹12,000",
      feeAfterDiscount: "₹38,000",
      oneTimeFee: "₹38,000",
      installments: {
        first: "₹25,000",
        second: "₹13,000 (Oct)"
      },
      features: [
        "Two-year comprehensive program",
        "NEET/JEE Main & Advanced prep",
        "PCM/PCB stream specialization",
        "Board + competitive exam focus",
        "Regular mock tests & analysis"
      ],
      badge: "Most Popular",
      badgeColor: "bg-purple-500"
    },
    {
      class: "Dropper's Batch",
      subtitle: "Class XII Passed",
      duration: "Per Annum",
      originalFee: "₹60,000",
      discount: "₹10,000",
      feeAfterDiscount: "₹50,000",
      oneTimeFee: "₹45,000",
      installments: {
        first: "₹30,000",
        second: "₹20,000 (Oct)"
      },
      features: [
        "One-year intensive preparation",
        "NEET-UG/JEE Main & Advanced",
        "AMU entrance preparation",
        "Crash course methodology",
        "Personalized mentoring"
      ],
      badge: "Intensive",
      badgeColor: "bg-red-500"
    },
    {
      class: "AMU Entrance",
      subtitle: "Class VIII + AMU IX",
      duration: "Per Annum",
      originalFee: "₹35,000",
      discount: "₹7,000",
      feeAfterDiscount: "₹28,000",
      oneTimeFee: "₹28,000",
      installments: {
        first: "₹18,000",
        second: "₹10,000 (Oct)"
      },
      features: [
        "Specialized AMU entrance preparation",
        "Class VIII foundation building",
        "AMU specific question patterns",
        "Regular mock tests",
        "Interview preparation guidance"
      ],
      badge: "Specialized",
      badgeColor: "bg-green-500"
    }
  ];

  const paymentMethods = [
    {
      icon: <Smartphone className="h-8 w-8 text-teal-600" />,
      title: "UPI Payment",
      description: "Pay instantly using Google Pay, PhonePe, Paytm"
    },
    {
      icon: <CreditCard className="h-8 w-8 text-teal-600" />,
      title: "Net Banking",
      description: "Secure payment through your bank account"
    },
    {
      icon: <Banknote className="h-8 w-8 text-teal-600" />,
      title: "Cash Payment",
      description: "Pay directly at our institute"
    }
  ];

  const scholarships = [
    {
      title: "Merit Scholarship",
      discount: "Up to 30%",
      criteria: "Based on previous academic performance"
    },
    {
      title: "Sibling Discount",
      discount: "15%",
      criteria: "For families with multiple enrollments"
    },
    {
      title: "Early Bird Offer",
      discount: "10%",
      criteria: "Register before course start date"
    }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-50 via-white to-amber-50">
          <div className="absolute top-20 left-10 w-72 h-72 bg-violet-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-amber-100 rounded-full px-4 py-2 mb-6 border border-violet-200/50 backdrop-blur-sm">
              <Star className="h-4 w-4 text-violet-600 mr-2 animate-pulse" />
              <span className="text-sm font-medium text-violet-700">Transparent & Affordable Pricing</span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold mb-6 relative">
              <div className="relative inline-block">
                <span className="bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent drop-shadow-sm">Fee Structure</span>
                <div className="absolute -top-4 -right-8 text-violet-500 opacity-80">
                  <DollarSign className="h-8 w-8 lg:h-10 lg:w-10 transform rotate-15 drop-shadow-md animate-bounce" />
                </div>
              </div>
              <br />
              <span className="text-gray-900 drop-shadow-sm">2025–26</span>
            </h1>

            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8 drop-shadow-sm">
              Transparent and affordable pricing for quality education at New Heights Classes.
              Comprehensive courses aligned with latest NEET/JEE/AMU patterns. Special discounts and flexible installment options available.
            </p>
            
            <div className="grid md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              <img
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                alt="Affordable Quality Education and NCERT Learning"
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
              <img
                src="https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                alt="Students Learning with Educational Books"
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
              <img
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                alt="Educational Excellence for All Students"
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
              <img
                src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                alt="Comprehensive Learning Environment"
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 justify-center">
            {pricingData.map((plan, index) => (
              <div key={index} className="relative bg-white rounded-2xl shadow-lg border-2 border-gray-100 hover:border-teal-200 transition-all duration-300">
                {plan.badge && (
                  <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 ${plan.badgeColor} text-white px-4 py-1 rounded-full text-sm font-medium`}>
                    {plan.badge}
                  </div>
                )}
                <div className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">{plan.class}</h3>
                    <p className="text-gray-600 text-sm mb-4">{plan.subtitle}</p>
                    <p className="text-gray-500 text-sm mb-4">{plan.duration}</p>
                    
                    <div className="mb-4">
                      <div className="text-lg text-gray-500 line-through mb-1">Total Annual Fee: {plan.originalFee}</div>
                      <div className="text-sm text-green-600 mb-2">Discount: {plan.discount}</div>
                      <div className="text-xl font-bold text-teal-600 mb-1">After Discount: {plan.feeAfterDiscount}</div>
                      <div className="text-lg font-semibold text-orange-600">One-time Payment: {plan.oneTimeFee}</div>
                    </div>
                    
                    <div className="bg-gray-50 p-3 rounded-lg text-sm">
                      <div className="font-medium text-gray-700 mb-1">Installment Options:</div>
                      <div className="text-gray-600">1st: {plan.installments.first}</div>
                      <div className="text-gray-600">2nd: {plan.installments.second}</div>
                    </div>
                  </div>
                  
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-teal-700 transition-colors duration-200 text-sm">
                    Enroll Now
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Payment Methods */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Payment Options
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Multiple convenient ways to pay for your courses
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {paymentMethods.map((method, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md text-center">
                <div className="bg-teal-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  {method.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{method.title}</h3>
                <p className="text-gray-600">{method.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Scholarships & Discounts */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Scholarships & Discounts
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe quality education should be accessible to all deserving students
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {scholarships.map((scholarship, index) => (
              <div key={index} className="bg-gradient-to-br from-teal-50 to-orange-50 rounded-xl p-6 text-center">
                <div className="bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Gift className="h-8 w-8 text-teal-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{scholarship.title}</h3>
                <div className="text-2xl font-bold text-teal-600 mb-3">{scholarship.discount}</div>
                <p className="text-gray-600">{scholarship.criteria}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-teal-600 to-teal-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-teal-100 mb-8">
            Join thousands of successful students who chose New Heights Classes for their bright future
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
              Get Free Counseling
            </button>
            <button className="bg-orange-500 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors duration-200">
              Enroll Now
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Pricing;