import { ArrowLeft, BookOpen, Users, Clock, Award, CheckCircle, Star, Trophy, Target, Calendar, Phone, GraduationCap, Globe } from 'lucide-react';
import { Link } from 'react-router-dom';

const JMIPreparation = () => {
  const courseHighlights = [
    "Jamia Millia Islamia entrance preparation",
    "Central university with national recognition",
    "Diverse undergraduate and postgraduate programs",
    "Strong alumni network across various fields",
    "Research-oriented university with modern facilities"
  ];

  const availableCourses = [
    {
      faculty: "Engineering & Technology",
      courses: ["B.Tech", "M.Tech", "MCA"],
      subjects: ["Physics", "Chemistry", "Mathematics", "Computer Science"]
    },
    {
      faculty: "Architecture & Ekistics",
      courses: ["B.Arch", "M.Arch", "B.Planning"],
      subjects: ["Mathematics", "Drawing", "General Aptitude"]
    },
    {
      faculty: "Mass Communication",
      courses: ["BJMC", "MJMC", "PhD"],
      subjects: ["English", "General Knowledge", "Current Affairs"]
    },
    {
      faculty: "Humanities & Languages",
      courses: ["BA", "MA", "M.Phil", "PhD"],
      subjects: ["English", "History", "Political Science", "Languages"]
    },
    {
      faculty: "Natural Sciences",
      courses: ["B.Sc", "M.Sc", "PhD"],
      subjects: ["Physics", "Chemistry", "Mathematics", "Biology"]
    },
    {
      faculty: "Social Sciences",
      courses: ["BA", "MA", "PhD"],
      subjects: ["Economics", "Psychology", "Sociology", "Geography"]
    }
  ];

  const examPattern = [
    {
      aspect: "Mode",
      detail: "Online (CBT) / Offline"
    },
    {
      aspect: "Duration",
      detail: "2-3 Hours (Course Specific)"
    },
    {
      aspect: "Questions",
      detail: "100-200 (Objective Type)"
    },
    {
      aspect: "Marking",
      detail: "Course Specific Pattern"
    },
    {
      aspect: "Language",
      detail: "English & Hindi"
    }
  ];
  const facultyMembers = [
    {
      name: "Prof. Salma Ahmad",
      subject: "English & General Studies",
      experience: "16+ Years",
      qualification: "M.A. English, JMI",
      specialization: "English Literature & Comprehension",
      achievements: "JMI Entrance Expert"
    },
    {
      name: "Dr. Mohd. Tariq",
      subject: "Science & Mathematics",
      experience: "14+ Years",
      qualification: "Ph.D. Physics, JMI",
      specialization: "Science Subjects for JMI",
      achievements: "Science Subject Specialist"
    },
    {
      name: "Prof. Rashida Khan",
      subject: "Social Sciences & Current Affairs",
      experience: "18+ Years",
      qualification: "M.A. Political Science, JMI",
      specialization: "Social Sciences & GK",
      achievements: "Humanities Teaching Expert"
    }
  ];

  const studyMaterial = [
    {
      title: "JMI Specific Study Material",
      description: "Customized content based on JMI entrance patterns",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Previous Year Papers",
      description: "Comprehensive collection of JMI entrance papers",
      icon: <Award className="h-6 w-6" />
    },
    {
      title: "Mock Test Series",
      description: "Regular practice tests following JMI patterns",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Current Affairs Updates",
      description: "Regular updates on current affairs and GK",
      icon: <Globe className="h-6 w-6" />
    }
  ];
  const successStats = [
    { number: "Expert", label: "Faculty", subtext: "JMI Alumni" },
    { number: "Quality", label: "Coaching", subtext: "Comprehensive" },
    { number: "16+", label: "Years Experience", subtext: "Combined Faculty" },
    { number: "Proven", label: "Track Record", subtext: "Success Stories" }
  ];

  const batchDetails = [
    {
      name: "Engineering Batch",
      duration: "6-8 Months",
      timing: "Morning: 7:00 AM - 12:00 PM",
      strength: "25 Students",
      features: ["B.Tech Focus", "PCM Coverage", "Technical Aptitude"]
    },
    {
      name: "Humanities Batch",
      duration: "4-6 Months",
      timing: "Afternoon: 1:00 PM - 5:00 PM",
      strength: "30 Students",
      features: ["Arts Focus", "Language Skills", "Essay Writing"]
    },
    {
      name: "Mass Communication Batch",
      duration: "3-4 Months",
      timing: "Evening: 5:00 PM - 8:00 PM",
      strength: "20 Students",
      features: ["BJMC/MJMC Focus", "Media Awareness", "Communication Skills"]
    }
  ];

  const specialFeatures = [
    {
      title: "Diverse Course Coverage",
      description: "Comprehensive preparation for all major JMI courses",
      icon: <BookOpen className="h-8 w-8" />
    },
    {
      title: "Alumni Network Support",
      description: "Guidance from successful JMI alumni",
      icon: <Users className="h-8 w-8" />
    },
    {
      title: "Regular Counseling",
      description: "Course selection and career guidance sessions",
      icon: <Target className="h-8 w-8" />
    },
    {
      title: "Updated Curriculum",
      description: "Latest syllabus and pattern-based preparation",
      icon: <Award className="h-8 w-8" />
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-50 to-red-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-8">
            <Link to="/courses" className="flex items-center text-orange-600 hover:text-orange-700 font-medium">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Courses
            </Link>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="bg-orange-600 p-3 rounded-xl mr-4">
                  <GraduationCap className="h-8 w-8 text-white" />
                </div>
                <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                  Central University
                </span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                JMI Entrance Preparation
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Comprehensive preparation for Jamia Millia Islamia entrance examinations 
                across Engineering, Architecture, Mass Communication, Humanities, and Sciences.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/admission"
                  className="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 text-center"
                >
                  Enroll Now
                </Link>
                <a
                  href="tel:+919058619887"
                  className="border-2 border-orange-600 text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors duration-200 text-center flex items-center justify-center"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Call for Info
                </a>
              </div>
            </div>
              <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="University Students Learning and Studying for JMI Entrance"
                className="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-orange-900/50 to-transparent rounded-2xl"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <h3 className="text-2xl font-bold mb-2">JMI Success Gateway</h3>
                <p className="text-orange-100">Excellence in Education</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-6">
                {successStats.map((stat, index) => (
                  <div key={index} className="bg-white p-4 rounded-xl shadow-lg text-center">
                    <div className="text-2xl font-bold text-orange-600 mb-1">{stat.number}</div>
                    <div className="font-semibold text-gray-900 text-sm mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About JMI */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              About JMI Entrance
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know about Jamia Millia Islamia entrance examinations
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-gradient-to-br from-orange-50 to-red-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Exam Pattern</h3>
              <div className="space-y-3">
                {examPattern.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="font-medium text-gray-700">{item.aspect}</span>
                    <span className="text-orange-600 font-semibold">{item.detail}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-teal-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Key Highlights</h3>
              <ul className="space-y-3">
                {courseHighlights.map((highlight, index) => (
                  <li key={index} className="flex items-start">
                    <Star className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Special Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our JMI Preparation?
            </h2>
            <p className="text-xl text-gray-600">
              Unique features that make us the best choice for JMI entrance coaching
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {specialFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-orange-600">{feature.icon}</div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}          </div>
          
          <div className="bg-gradient-to-br from-orange-50 to-red-50 p-8 rounded-2xl mt-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Educational Resources & Study Materials</h3>
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">NCERT-Based Foundation</h4>
                      <p className="text-gray-600">Complete NCERT coverage aligned with JMI entrance requirements</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">JMI-Specific Study Materials</h4>
                      <p className="text-gray-600">Specialized content designed for different JMI faculties</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Modern Learning Environment</h4>
                      <p className="text-gray-600">Contemporary teaching methods with hands-on learning approach</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <img
                  src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Educational Books and NCERT Materials for JMI Preparation"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
                <img
                  src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Students Engaged in Active Learning"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
                <img
                  src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="University Education and Learning Environment"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
                <img
                  src="https://images.unsplash.com/photo-1434494878577-86c23bcb06b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Academic Study Environment for University Entrance"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Available Courses */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Courses
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive preparation for all major faculties at JMI
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {availableCourses.map((course, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-orange-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="bg-orange-100 p-3 rounded-xl mr-4">
                    <BookOpen className="h-6 w-6 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">{course.faculty}</h3>
                </div>
                
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Available Programs:</h4>
                  <div className="flex flex-wrap gap-2">
                    {course.courses.map((program, idx) => (
                      <span key={idx} className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-sm">
                        {program}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Subjects Covered:</h4>
                  <ul className="space-y-1">
                    {course.subjects.map((subject, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{subject}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Expert Faculty Team
            </h2>
            <p className="text-xl text-gray-600">
              Learn from JMI alumni and experienced entrance coaching specialists
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-10 w-10 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{faculty.name}</h3>
                <div className="text-orange-600 font-semibold mb-2">{faculty.subject}</div>
                <div className="text-sm text-gray-600 mb-4">
                  <div>{faculty.qualification}</div>
                  <div>{faculty.experience}</div>
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-1">Specialization</div>
                  <div className="text-sm text-gray-600">{faculty.specialization}</div>
                </div>
                
                <div className="flex items-center justify-center text-sm text-orange-600 font-semibold">
                  <Trophy className="h-4 w-4 mr-1" />
                  {faculty.achievements}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Study Material
            </h2>
            <p className="text-xl text-gray-600">
              Specially designed materials for JMI entrance success
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="bg-white w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-md">
                  <div className="text-orange-600">{material.icon}</div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600 text-sm">{material.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Batches
            </h2>
            <p className="text-xl text-gray-600">
              Specialized batches for different faculties and programs
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border-t-4 border-orange-500">
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-orange-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{batch.name}</h3>
                </div>
                
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Duration:</strong> {batch.duration}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Timing:</strong> {batch.timing}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Users className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Batch Size:</strong> {batch.strength}</span>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Link
                  to="/admission"
                  className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 text-center block"
                >
                  Select This Batch
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-orange-600 to-red-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Join JMI?
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            Transform your dreams into reality with expert guidance at New Heights Classes
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Apply Now
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-orange-600 transition-colors duration-200"
            >
              Get Free Counseling
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default JMIPreparation;
