import { Book<PERSON><PERSON>, Users, Clock, Award, Download, CheckCircle, ArrowR<PERSON>, Star, <PERSON>, Brain } from 'lucide-react';
import { Link } from 'react-router-dom';

const Courses = () => {
  const courses = [
    {
      title: "Pre Foundation Course",
      subtitle: "Class IX-X Foundation Years",
      duration: "2 Years",
      pattern: "NCERT Foundation + Early Competitive Exposure",
      subjects: ["Mathematics", "Physics", "Chemistry", "Biology", "English", "Mental Ability"],
      keyPoints: [
        "Strong NCERT-based foundation",
        "Early competitive preparation", 
        "Olympiad & NTSE training",
        "Study habits development"
      ],
      icon: <BookOpen className="h-8 w-8" />,
      color: "bg-emerald-600",
      gradient: "from-emerald-500 to-teal-600",
      students: "150+",
      route: "/courses/pre-foundation"
    },
    {
      title: "Foundation Course (XI-XII)",
      subtitle: "Board + NEET/JEE Integrated",
      duration: "2 Years",
      pattern: "Dual Focus: Board Excellence + Competitive Success",
      subjects: ["Physics", "Chemistry", "Mathematics", "Biology", "Board Exam Preparation"],
      keyPoints: [
        "Complete NEET/JEE syllabus",
        "Latest NTA pattern alignment",
        "PCM/PCB stream flexibility",
        "Board + competitive integration"
      ],
      icon: <Award className="h-8 w-8" />,
      color: "bg-purple-600",
      gradient: "from-purple-500 to-violet-600",
      students: "200+",
      route: "/courses/foundation"
    },
    {
      title: "Dropper's Batch",
      subtitle: "12th Pass/Repeaters Intensive",
      duration: "1 Year Intensive",
      pattern: "Result-Oriented Intensive Preparation",
      subjects: ["Physics", "Chemistry", "Mathematics", "Biology", "Test Series", "Revision"],
      keyPoints: [
        "Intensive syllabus revision",
        "Weak area improvement",
        "Daily practice sessions",
        "Psychological counseling"
      ],
      icon: <Clock className="h-8 w-8" />,
      color: "bg-rose-600",
      gradient: "from-rose-500 to-pink-600",
      students: "120+",
      route: "/courses/dropper"
    },
    {
      title: "AMU Entrance Course",
      subtitle: "Class VIII + AMU IX Specialized",
      duration: "6-12 Months",
      pattern: "AMU Entrance Test + Academic Excellence",
      subjects: ["AMU Syllabus", "General Knowledge", "English", "Logical Reasoning", "Interview Skills"],
      keyPoints: [
        "Complete AMU entrance coverage",
        "English proficiency focus",
        "Interview preparation",
        "Campus guidance"
      ],
      icon: <Users className="h-8 w-8" />,
      color: "bg-indigo-600",
      gradient: "from-indigo-500 to-blue-600",
      students: "80+",
      route: "/courses/amu"
    }
  ];

  const studyFeatures = [
    {
      icon: <Users className="h-8 w-8 text-teal-600" />,
      title: "Expert Faculty & Small Batches",
      description: "Highly qualified teachers with proven track record, maintaining optimal batch sizes for personalized attention"
    },
    {
      icon: <Award className="h-8 w-8 text-teal-600" />,
      title: "Latest NTA Pattern Tests",
      description: "Regular mock tests following current NEET/JEE patterns with detailed analysis and performance tracking"
    },
    {
      icon: <BookOpen className="h-8 w-8 text-teal-600" />,
      title: "NCERT-Based Study Material",
      description: "Comprehensive study material aligned with NCERT/CBSE guidelines and latest syllabus updates"
    },
    {
      icon: <Clock className="h-8 w-8 text-teal-600" />,
      title: "Flexible Learning Options",
      description: "Multiple batch timings, doubt clearing sessions, and online support for continuous learning"
    }
  ];

  const benefits = [
    "Small batch sizes (15-20 students) for personalized attention",
    "Expert faculty with IIT/NIT/Medical background and proven results",
    "Latest NTA pattern mock tests and comprehensive performance analysis",
    "NCERT-based study material with advanced problem-solving techniques",
    "Individual doubt clearing sessions and mentorship programs",
    "Extensive previous year question papers and chapter-wise practice",
    "Regular parent-teacher meetings and progress tracking",
    "Career counseling, college selection, and admission guidance",
    "Flexible batch timings and make-up classes for missed sessions",
    "Online support and digital learning resources access"
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-50 via-white to-amber-50">
          <div className="absolute top-20 left-10 w-72 h-72 bg-violet-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-amber-100 rounded-full px-4 py-2 mb-6 border border-violet-200/50 backdrop-blur-sm">
              <Star className="h-4 w-4 text-violet-600 mr-2 animate-pulse" />
              <span className="text-sm font-medium text-violet-700">Comprehensive Course Programs</span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold mb-6 relative">
              <span className="text-gray-900 drop-shadow-sm">Our </span>
              <div className="relative inline-block">
                <span className="bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent drop-shadow-sm">Courses</span>
                <div className="absolute -top-4 -right-8 text-violet-500 opacity-80">
                  <Brain className="h-8 w-8 lg:h-10 lg:w-10 transform rotate-15 drop-shadow-md animate-bounce" />
                </div>
              </div>
            </h1>

            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed drop-shadow-sm">
              Comprehensive coaching programs designed for academic excellence and competitive success. From Pre-Foundation (Class IX-X)
              to specialized Dropper's Batch, including Foundation Course (XI-XII) with NEET/JEE integration and AMU Entrance preparation.
              Expert faculty delivering latest NTA-aligned curriculum with NCERT/CBSE standards for guaranteed results.
            </p>
          </div>
        </div>
      </section>

      {/* Courses Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 lg:gap-8">
            {courses.map((course, index) => (
              <div key={index} className={`relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer`}>
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${course.gradient} opacity-90`}></div>
                
                {/* Content */}
                <div className="relative p-6 text-white h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="bg-white bg-opacity-20 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                      {course.icon}
                    </div>
                    <div className="text-right">
                      <div className="text-xs opacity-80">{course.students} Students</div>
                      <div className="text-sm font-medium">{course.duration}</div>
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold mb-2">{course.title}</h3>
                  <p className="text-sm opacity-90 mb-4">{course.subtitle}</p>

                  {/* Key Points */}
                  <div className="flex-1 mb-6">
                    <ul className="space-y-2">
                      {course.keyPoints.map((point, idx) => (
                        <li key={idx} className="flex items-start text-sm opacity-90">
                          <CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                          {point}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Subjects Tags */}
                  <div className="mb-6">
                    <div className="flex flex-wrap gap-1">
                      {course.subjects.slice(0, 3).map((subject, idx) => (
                        <span key={idx} className="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">
                          {subject}
                        </span>
                      ))}
                      {course.subjects.length > 3 && (
                        <span className="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">
                          +{course.subjects.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 mt-auto">
                    <Link 
                      to={course.route}
                      className="flex-1 bg-white text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-center font-medium text-sm"
                    >
                      View Details
                    </Link>
                    <Link 
                      to="/admission"
                      className="bg-white bg-opacity-20 text-white py-2 px-4 rounded-lg hover:bg-opacity-30 transition-colors duration-200 text-center font-medium text-sm border border-white border-opacity-30"
                    >
                      Enroll
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Course Preview */}
      <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What You Get with Each Course
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Click on any course to explore detailed curriculum, faculty profiles, and success stories
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-md text-center">
              <BookOpen className="h-12 w-12 text-teal-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Detailed Syllabus</h3>
              <p className="text-gray-600">Complete chapter-wise breakdown, topic coverage, and learning objectives for each subject</p>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-md text-center">
              <Users className="h-12 w-12 text-teal-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Expert Faculty Profiles</h3>
              <p className="text-gray-600">Meet our experienced teachers, their qualifications, and teaching methodologies</p>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-md text-center">
              <Award className="h-12 w-12 text-teal-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Success Stories & Results</h3>
              <p className="text-gray-600">Real student achievements, rankings, and testimonials from our course alumni</p>
            </div>
          </div>
        </div>
      </section>

      {/* Course Specializations */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Specialized Tracks & Target Exams
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Each course offers specialized tracks to help you achieve your specific goals
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="bg-emerald-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <BookOpen className="h-6 w-6 text-emerald-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">NEET Track</h3>
              <p className="text-gray-600 text-sm mb-4">Medical entrance preparation with NCERT focus, Biology mastery, and NEET-specific problem solving</p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>• Complete NCERT Biology coverage</li>
                <li>• Medical diagrams & practicals</li>
                <li>• NEET previous year analysis</li>
                <li>• Medical counseling guidance</li>
              </ul>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">JEE Track</h3>
              <p className="text-gray-600 text-sm mb-4">Engineering entrance preparation with advanced Mathematics, Physics concepts, and JEE problem techniques</p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>• Advanced Mathematics mastery</li>
                <li>• Physics conceptual clarity</li>
                <li>• JEE Main & Advanced patterns</li>
                <li>• Engineering counseling</li>
              </ul>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="bg-rose-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-rose-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Board Excellence</h3>
              <p className="text-gray-600 text-sm mb-4">CBSE/State Board mastery with 90%+ target, internal assessments, and board exam techniques</p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>• 90%+ score targeting</li>
                <li>• Board exam techniques</li>
                <li>• Internal assessment prep</li>
                <li>• Answer writing skills</li>
              </ul>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="bg-indigo-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-indigo-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">AMU Special</h3>
              <p className="text-gray-600 text-sm mb-4">Aligarh Muslim University entrance preparation with subject-specific focus and interview skills</p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>• AMU-specific syllabus</li>
                <li>• English proficiency focus</li>
                <li>• Interview preparation</li>
                <li>• Previous year analysis</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Success Statistics */}
      <section className="py-16 bg-gradient-to-r from-teal-600 to-teal-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Our Success Record (2024)
            </h2>
            <p className="text-xl text-teal-100 max-w-3xl mx-auto">
              Proven results that speak for our quality education and student success
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">95%</div>
              <div className="text-teal-100 font-medium">Board Exam Success</div>
              <div className="text-sm text-teal-200">Students scoring 85%+</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">87%</div>
              <div className="text-teal-100 font-medium">NEET Qualified</div>
              <div className="text-sm text-teal-200">Medical college admissions</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">82%</div>
              <div className="text-teal-100 font-medium">JEE Success Rate</div>
              <div className="text-sm text-teal-200">Engineering admissions</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">78%</div>
              <div className="text-teal-100 font-medium">AMU Entrance</div>
              <div className="text-sm text-teal-200">Successful admissions</div>
            </div>
          </div>
        </div>
      </section>

      {/* Study Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Study Features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need for successful learning and exam preparation
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md text-center">
                <div className="bg-teal-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose New Heights Classes?
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <img
                src="https://images.pexels.com/photos/8500749/pexels-photo-8500749.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="Students studying NCERT books for NEET JEE preparation in coaching institute"
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Download CTA */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Download Complete Course Brochure 2025
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Get detailed syllabus, fee structure, batch timings, faculty profiles, and admission process for all courses. 
            Includes latest NEET/JEE pattern updates and success stories from our toppers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="inline-flex items-center bg-teal-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-teal-700 transition-colors duration-200">
              <Download className="mr-2 h-5 w-5" />
              Download PDF (2MB)
            </button>
            <Link 
              to="/contact"
              className="inline-flex items-center bg-gray-700 text-white px-8 py-4 rounded-lg font-semibold hover:bg-gray-600 transition-colors duration-200 border border-gray-600"
            >
              Schedule Campus Visit
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Courses;