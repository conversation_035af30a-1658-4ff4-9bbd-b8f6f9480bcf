import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { coursesData } from '../data/coursesData';

const HeroCarousel: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % coursesData.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % coursesData.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + coursesData.length) % coursesData.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const currentCourse = coursesData[currentSlide];
  const IconComponent = currentCourse.icon;

  return (
    <div
      className="relative h-full w-full overflow-hidden rounded-lg sm:rounded-xl md:rounded-2xl bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 shadow-xl touch-manipulation"
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
      onTouchStart={() => setIsAutoPlaying(false)}
      onTouchEnd={() => setIsAutoPlaying(true)}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-violet-500/20 to-amber-500/20"></div>
        <div className="absolute top-3 left-3 sm:top-5 sm:left-5 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 bg-violet-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-3 right-3 sm:bottom-5 sm:right-5 w-20 h-20 sm:w-28 sm:h-28 md:w-40 md:h-40 bg-amber-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000"></div>
      </div>

      {/* Main Content - Mobile Optimized */}
      <div className="relative h-full flex flex-col justify-between p-3 xs:p-4 sm:p-5 lg:p-8">
        {/* Course Icon & Badge */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r ${currentCourse.gradient} shadow-lg`}>
              <IconComponent className="h-5 w-5 xs:h-6 xs:w-6 sm:h-7 sm:w-7 text-white" />
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-full px-2 sm:px-3 py-1 sm:py-1.5 border border-white/20">
              <span className="text-white/90 text-xs sm:text-sm font-medium">{currentCourse.duration}</span>
            </div>
          </div>
        </div>

        {/* Course Content - Mobile Responsive */}
        <div className="flex-1 flex flex-col justify-center space-y-2 xs:space-y-3 sm:space-y-4">
          <div>
            <h3 className="text-base xs:text-lg sm:text-xl lg:text-3xl font-bold text-white mb-1 sm:mb-2 leading-tight">
              {currentCourse.title}
            </h3>
            <p className="text-xs xs:text-sm sm:text-base lg:text-lg text-white/80 mb-2 sm:mb-3">
              {currentCourse.subtitle}
            </p>
            <p className="text-white/70 text-xs xs:text-sm lg:text-base leading-relaxed max-w-sm line-clamp-2 sm:line-clamp-none">
              {currentCourse.description}
            </p>
          </div>

          {/* Highlights */}
          <div className="flex flex-wrap gap-1 sm:gap-1.5">
            {currentCourse.highlights.slice(0, 3).map((highlight, index) => (
              <span
                key={index}
                className="bg-white/10 backdrop-blur-sm text-white/90 px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-xs border border-white/20"
              >
                {highlight}
              </span>
            ))}
          </div>

          {/* CTA Button - Mobile Optimized */}
          <Link
            to={currentCourse.route}
            className="inline-flex items-center justify-center space-x-1.5 sm:space-x-2 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 active:from-violet-700 active:to-purple-800 text-white px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 sm:py-3 rounded-lg sm:rounded-xl font-semibold text-xs xs:text-sm sm:text-base transition-all duration-300 transform hover:scale-105 active:scale-95 hover:shadow-xl w-fit group touch-manipulation mobile-button"
          >
            <span>Explore Course</span>
            <ArrowRight className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>

        {/* Navigation Controls - Mobile Enhanced */}
        <div className="flex items-center justify-between">
          {/* Slide Indicators */}
          <div className="flex space-x-1 sm:space-x-1.5 md:space-x-2">
            {coursesData.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-1.5 h-1.5 xs:w-2 xs:h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 touch-manipulation mobile-button ${
                  index === currentSlide
                    ? 'bg-white shadow-lg scale-125'
                    : 'bg-white/30 hover:bg-white/50 active:bg-white/60'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Arrow Controls - Touch Optimized */}
          <div className="flex space-x-1 sm:space-x-2">
            <button
              onClick={prevSlide}
              className="p-1.5 xs:p-2 sm:p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 active:bg-white/30 transition-all duration-300 hover:scale-110 active:scale-95 touch-manipulation mobile-button"
              aria-label="Previous slide"
            >
              <ChevronLeft className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5" />
            </button>
            <button
              onClick={nextSlide}
              className="p-1.5 xs:p-2 sm:p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 active:bg-white/30 transition-all duration-300 hover:scale-110 active:scale-95 touch-manipulation mobile-button"
              aria-label="Next slide"
            >
              <ChevronRight className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Slide Transition Effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div className={`absolute inset-0 bg-gradient-to-r ${currentCourse.gradient} opacity-20 transition-all duration-1000`}></div>
      </div>
    </div>
  );
};

export default HeroCarousel;
