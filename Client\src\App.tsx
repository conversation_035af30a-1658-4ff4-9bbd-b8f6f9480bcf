import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import About from './pages/About';
import Courses from './pages/Courses';
import Pricing from './pages/Pricing';
import Contact from './pages/Contact';
import Admission from './pages/Admission';
import NotFound from './pages/NotFound';
import PreFoundation from './pages/courses/PreFoundation';
import Foundation from './pages/courses/Foundation';
import Dropper from './pages/courses/Dropper';
import AMUEntrance from './pages/courses/AMUEntrance';

const App = () => {
  return (
    <Router>
      <div className="min-h-screen bg-white">
        <ScrollToTop />
        <Navbar />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/courses" element={<Courses />} />
            <Route path="/courses/pre-foundation" element={<PreFoundation />} />
            <Route path="/courses/foundation" element={<Foundation />} />
            <Route path="/courses/dropper" element={<Dropper />} />
            <Route path="/courses/amu" element={<AMUEntrance />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/admission" element={<Admission />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
};

export { App };
export default App;