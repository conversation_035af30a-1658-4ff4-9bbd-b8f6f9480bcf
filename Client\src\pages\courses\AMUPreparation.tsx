import { ArrowLeft, BookOpen, Users, Clock, Award, CheckCircle, Star, Trophy, Target, Calendar, Phone, GraduationCap } from 'lucide-react';
import { Link } from 'react-router-dom';

const AMUPreparation = () => {
  const courseHighlights = [
    "Aligarh Muslim University entrance preparation",
    "Multiple undergraduate and postgraduate programs",
    "One of India's premier central universities",
    "Diverse course offerings across faculties",
    "Legacy of excellence in education since 1875"
  ];

  const availableCourses = [
    {
      faculty: "Engineering & Technology",
      courses: ["B.Tech", "B.Arch", "M.Tech", "M.Arch"],
      subjects: ["Physics", "Chemistry", "Mathematics"]
    },
    {
      faculty: "Medicine",
      courses: ["MBBS", "BDS", "BUMS", "BHMS"],
      subjects: ["Physics", "Chemistry", "Biology"]
    },
    {
      faculty: "Management Studies",
      courses: ["MBA", "BBA"],
      subjects: ["General Aptitude", "English", "Quantitative Ability"]
    },
    {
      faculty: "Arts & Humanities",
      courses: ["BA", "MA", "PhD"],
      subjects: ["English", "General Knowledge", "Subject Specific"]
    },
    {
      faculty: "Science",
      courses: ["B.Sc", "M.Sc", "PhD"],
      subjects: ["Physics", "Chemistry", "Mathematics", "Biology"]
    },
    {
      faculty: "Social Sciences",
      courses: ["B.A", "M.A", "PhD"],
      subjects: ["History", "Political Science", "Economics", "Sociology"]
    }
  ];

  const examPattern = [
    {
      aspect: "Mode",
      detail: "Online (Computer Based Test)"
    },
    {
      aspect: "Duration",
      detail: "2-3 Hours (Course Specific)"
    },
    {
      aspect: "Questions",
      detail: "100-150 (Multiple Choice)"
    },
    {
      aspect: "Marking",
      detail: "+4 for correct, -1 for wrong"
    },
    {
      aspect: "Language",
      detail: "English & Urdu"
    }
  ];
  const facultyMembers = [
    {
      name: "Prof. Mohammed Hasan",
      subject: "General Studies & English",
      experience: "20+ Years",
      qualification: "M.A. English, AMU",
      specialization: "AMU Entrance Pattern & English",
      achievements: "AMU Entrance Expert"
    },
    {
      name: "Dr. Fatima Khan",
      subject: "Science Subjects",
      experience: "15+ Years",
      qualification: "Ph.D. Chemistry, AMU",
      specialization: "PCM/PCB for AMU",
      achievements: "Science Subject Specialist"
    },
    {
      name: "Prof. Arif Siddiqui",
      subject: "Quantitative Aptitude",
      experience: "18+ Years",
      qualification: "M.A. Mathematics, AMU",
      specialization: "Mathematical Reasoning & Aptitude",
      achievements: "Mathematics Teaching Expert"
    }
  ];

  const studyMaterial = [
    {
      title: "AMU Specific Study Material",
      description: "Customized notes based on AMU entrance pattern and syllabus",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Previous Year Papers",
      description: "10+ years AMU entrance papers with detailed solutions",
      icon: <Award className="h-6 w-6" />
    },
    {
      title: "Mock Test Series",
      description: "Regular tests following exact AMU entrance pattern",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Interview Preparation",
      description: "Personality development and interview guidance",
      icon: <Users className="h-6 w-6" />
    }
  ];
  const successStats = [
    { number: "New", label: "Institute", subtext: "Established 2025" },
    { number: "Expert", label: "Faculty", subtext: "AMU Alumni" },
    { number: "Quality", label: "Teaching", subtext: "Focused Approach" },
    { number: "20+", label: "Years Experience", subtext: "Combined Faculty" }
  ];

  const batchDetails = [
    {
      name: "Engineering Batch",
      duration: "6-8 Months",
      timing: "Morning: 7:00 AM - 12:00 PM",
      strength: "30 Students",
      features: ["PCM Focus", "B.Tech Preparation", "Advanced Problem Solving"]
    },
    {
      name: "Medical Batch",
      duration: "6-8 Months",
      timing: "Afternoon: 1:00 PM - 6:00 PM",
      strength: "25 Students",
      features: ["PCB Focus", "MBBS/BDS Preparation", "Medical Entrance Training"]
    },
    {
      name: "Management Batch",
      duration: "4-6 Months",
      timing: "Evening: 5:00 PM - 9:00 PM",
      strength: "35 Students",
      features: ["MBA/BBA Focus", "Aptitude Training", "Interview Preparation"]
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 to-indigo-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-8">
            <Link to="/courses" className="flex items-center text-purple-600 hover:text-purple-700 font-medium">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Courses
            </Link>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="bg-purple-600 p-3 rounded-xl mr-4">
                  <GraduationCap className="h-8 w-8 text-white" />
                </div>
                <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                  Central University
                </span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                AMU Entrance Preparation
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Comprehensive preparation for Aligarh Muslim University entrance examinations 
                across various faculties including Engineering, Medicine, Management, and Arts.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/admission"
                  className="bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 text-center"
                >
                  Enroll Now
                </Link>
                <a
                  href="tel:+919058619887"
                  className="border-2 border-purple-600 text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors duration-200 text-center flex items-center justify-center"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Call for Info
                </a>
              </div>            </div>
            
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="University Students Studying for AMU Entrance Examination"
                className="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-purple-900/50 to-transparent rounded-2xl"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <h3 className="text-2xl font-bold mb-2">AMU Success Gateway</h3>
                <p className="text-purple-100">Your Path to Central University</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-6">
                {successStats.map((stat, index) => (                  <div key={index} className="bg-white p-4 rounded-xl shadow-lg text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">{stat.number}</div>
                    <div className="font-semibold text-gray-900 text-sm mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About AMU */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              About AMU Entrance
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know about Aligarh Muslim University entrance examinations
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Exam Pattern</h3>
              <div className="space-y-3">
                {examPattern.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
                    <span className="font-medium text-gray-700">{item.aspect}</span>
                    <span className="text-purple-600 font-semibold">{item.detail}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-teal-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Key Highlights</h3>
              <ul className="space-y-3">
                {courseHighlights.map((highlight, index) => (
                  <li key={index} className="flex items-start">
                    <Star className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Available Courses */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Courses
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive preparation for all major faculties at AMU
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {availableCourses.map((course, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="bg-purple-100 p-3 rounded-xl mr-4">
                    <BookOpen className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">{course.faculty}</h3>
                </div>
                
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Available Programs:</h4>
                  <div className="flex flex-wrap gap-2">
                    {course.courses.map((program, idx) => (
                      <span key={idx} className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm">
                        {program}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Subjects Covered:</h4>
                  <ul className="space-y-1">
                    {course.subjects.map((subject, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{subject}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Expert Faculty Team
            </h2>
            <p className="text-xl text-gray-600">
              Learn from AMU alumni and experienced entrance coaching experts
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-purple-50 rounded-2xl p-6 shadow-lg text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-10 w-10 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{faculty.name}</h3>
                <div className="text-purple-600 font-semibold mb-2">{faculty.subject}</div>
                <div className="text-sm text-gray-600 mb-4">
                  <div>{faculty.qualification}</div>
                  <div>{faculty.experience}</div>
                </div>
                
                <div className="bg-white p-3 rounded-lg mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-1">Specialization</div>
                  <div className="text-sm text-gray-600">{faculty.specialization}</div>
                </div>
                
                <div className="flex items-center justify-center text-sm text-purple-600 font-semibold">
                  <Trophy className="h-4 w-4 mr-1" />
                  {faculty.achievements}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Material */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Study Material
            </h2>
            <p className="text-xl text-gray-600">
              Specially designed materials for AMU entrance success
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyMaterial.map((material, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <div className="text-purple-600">{material.icon}</div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{material.title}</h3>
                <p className="text-gray-600 text-sm">{material.description}</p>
              </div>
            ))}          </div>
          
          <div className="bg-gradient-to-br from-purple-50 to-indigo-50 p-8 rounded-2xl mt-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Educational Resources & Study Environment</h3>
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">NCERT Foundation Books</h4>
                      <p className="text-gray-600">Complete coverage of NCERT textbooks for all subjects</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">AMU-Specific Resources</h4>
                      <p className="text-gray-600">Specialized study materials aligned with AMU entrance pattern</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Interactive Learning</h4>
                      <p className="text-gray-600">Modern teaching methods with practical learning approach</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <img
                  src="https://images.unsplash.com/photo-1456513080510-7bf3a84b82d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="NCERT Books and Study Materials for AMU Preparation"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
                <img
                  src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Students Learning in Educational Environment"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
                <img
                  src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="University Education and Learning Books"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
                <img
                  src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Students Studying for University Entrance Exams"
                  className="w-full h-48 object-cover rounded-xl shadow-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Batches
            </h2>
            <p className="text-xl text-gray-600">
              Specialized batches for different faculties and courses
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 shadow-lg">
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-purple-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{batch.name}</h3>
                </div>
                
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Duration:</strong> {batch.duration}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Timing:</strong> {batch.timing}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Users className="h-4 w-4 text-gray-500 mr-2" />
                    <span><strong>Batch Size:</strong> {batch.strength}</span>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Link
                  to="/admission"
                  className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 text-center block"
                >
                  Select This Batch
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-purple-600 to-indigo-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Join AMU?
          </h2>
          <p className="text-xl text-purple-100 mb-8">
            Secure your admission to one of India's most prestigious central universities
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Apply Now
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-200"
            >
              Get Free Counseling
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AMUPreparation;
