import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, Users, CheckCircle, Calendar, Target, TrendingUp, Award } from 'lucide-react';

const Dropper = () => {
  const courseFeatures = [
    "Intensive revision and mastery of complete NEET/JEE syllabus",
    "Focused improvement on weak areas through diagnostic assessments",
    "Latest exam pattern mastery with emphasis on new question types",
    "Daily practice sessions, weekly tests, and full-length mock examinations",
    "Psychological counseling, motivation sessions, and stress management",
    "Success stories sharing and guidance from previous year toppers",
    "Personalized study plans and individual performance analysis",
    "Strategic time management and exam temperament development"
  ];

  const studyPlan = [
    { phase: "Phase 1 (Months 1-3)", focus: "Complete Syllabus Revision", activities: ["Concept Revision", "Basic Problem Solving", "Weak Area Identification"] },
    { phase: "Phase 2 (Months 4-6)", focus: "Advanced Problem Solving", activities: ["Difficult Problems", "Previous Year Questions", "Topic-wise Tests"] },
    { phase: "Phase 3 (Months 7-9)", focus: "Test Series & Analysis", activities: ["Full Length Tests", "Performance Analysis", "Strategy Refinement"] },
    { phase: "Phase 4 (Months 10-12)", focus: "Final Preparation", activities: ["Revision", "Mock Tests", "Exam Strategy"] }
  ];

  const successStats = [
    { year: "2024", neet: "89%", jee: "85%", topRank: "AIR 142 NEET" },
    { year: "2023", neet: "87%", jee: "82%", topRank: "AIR 268 JEE" },
    { year: "2022", neet: "85%", jee: "80%", topRank: "AIR 189 NEET" }
  ];

  const batchDetails = [
    { timing: "NEET Dropper", time: "8:00 AM - 2:00 PM", days: "Monday to Saturday", strength: "25 students" },
    { timing: "JEE Dropper", time: "8:00 AM - 2:00 PM", days: "Monday to Saturday", strength: "25 students" },
    { timing: "Mixed Batch", time: "2:00 PM - 8:00 PM", days: "Monday to Saturday", strength: "30 students" }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Header */}
      <section className="bg-gradient-to-r from-rose-600 to-pink-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/courses" className="inline-flex items-center text-rose-100 hover:text-white mb-6 transition-colors">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Courses
          </Link>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Dropper's Batch</h1>
              <p className="text-xl text-rose-100 mb-6">12th Pass/Repeaters - Intensive NEET/JEE Preparation</p>
              <p className="text-lg text-rose-50 leading-relaxed mb-8">
                Transform your dreams into reality with our intensive one-year program designed specifically for 
                12th pass students and repeaters. Focused preparation with proven strategies for NEET/JEE success.
              </p>
              
              <div className="flex flex-wrap gap-4 mb-8">
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Clock className="h-5 w-5 inline mr-2" />
                  1 Year Intensive
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Users className="h-5 w-5 inline mr-2" />
                  120+ Students
                </div>
                <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                  <Target className="h-5 w-5 inline mr-2" />
                  Result Focused
                </div>
              </div>
              
              <Link 
                to="/admission" 
                className="bg-white text-rose-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
              >
                Enroll Now
                <ArrowLeft className="h-5 w-5 ml-2 rotate-180" />
              </Link>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400"
                alt="Dropper's batch students in intensive study session"
                className="rounded-2xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Success Statistics */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Our Success Record</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {successStats.map((stat, index) => (
              <div key={index} className="bg-gradient-to-br from-rose-50 to-pink-50 rounded-xl p-6 text-center">
                <div className="text-2xl font-bold text-rose-600 mb-4">{stat.year}</div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">NEET Success:</span>
                    <span className="font-bold text-rose-600">{stat.neet}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">JEE Success:</span>
                    <span className="font-bold text-rose-600">{stat.jee}</span>
                  </div>
                  <div className="mt-4 p-3 bg-white rounded-lg">
                    <div className="text-sm text-gray-500">Best Rank</div>
                    <div className="font-bold text-rose-600">{stat.topRank}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Course Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Course Highlights</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {courseFeatures.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-rose-500 mt-1 flex-shrink-0" />
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Plan */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">12-Month Study Plan</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {studyPlan.map((phase, index) => (
              <div key={index} className="relative">
                <div className="bg-rose-50 rounded-xl p-6 h-full">
                  <div className="absolute -top-3 -left-3 w-8 h-8 bg-rose-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                    {index + 1}
                  </div>
                  <h3 className="text-lg font-bold text-rose-600 mb-2 mt-2">{phase.phase}</h3>
                  <p className="text-gray-800 font-medium mb-4">{phase.focus}</p>
                  <ul className="space-y-2">
                    {phase.activities.map((activity, actIndex) => (
                      <li key={actIndex} className="flex items-center text-sm text-gray-600">
                        <TrendingUp className="h-4 w-4 text-rose-400 mr-2" />
                        {activity}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Batch Timings */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Available Batches</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center">
                <Calendar className="h-12 w-12 text-rose-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">{batch.timing}</h3>
                <p className="text-rose-600 font-medium mb-2">{batch.time}</p>
                <p className="text-gray-600 mb-2">{batch.days}</p>
                <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">{batch.strength}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Motivational Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Award className="h-16 w-16 text-rose-400 mx-auto mb-6" />
          <h2 className="text-3xl font-bold mb-4">"Success is not final, failure is not fatal: it is the courage to continue that counts."</h2>
          <p className="text-xl text-gray-300 mb-8">
            Join hundreds of students who turned their setback into a comeback. Your second attempt can be your best attempt.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/admission"
              className="bg-rose-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-rose-700 transition-colors"
            >
              Start Your Comeback
            </Link>
            <Link 
              to="/contact"
              className="border-2 border-gray-400 text-gray-300 px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 hover:text-white transition-colors"
            >
              Talk to Counselor
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Dropper;
