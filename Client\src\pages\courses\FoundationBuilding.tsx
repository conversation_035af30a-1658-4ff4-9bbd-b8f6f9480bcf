import { ArrowLeft, BookOpen, Users, Award, CheckCircle, Trophy, Target, Calendar, Phone, GraduationCap } from 'lucide-react';
import { Link } from 'react-router-dom';

const FoundationBuilding = () => {
  const courseHighlights = [
    "Strong foundation for future competitive success",
    "Class IX comprehensive curriculum coverage",
    "Early introduction to competitive patterns",
    "Conceptual clarity and problem-solving skills",
    "Preparation for Class X board exams with excellence"
  ];

  const subjects = {
    Science: [
      "Matter in Our Surroundings",
      "Atoms and Molecules", 
      "Structure of the Atom",
      "Motion and Force",
      "Gravitation and Flotation",
      "Work and Energy",
      "Sound and Light",
      "Natural Resources"
    ],
    Mathematics: [
      "Number Systems",
      "Polynomials",
      "Coordinate Geometry",
      "Linear Equations in Two Variables",
      "Introduction to Euclid's Geometry",
      "Lines and Angles",
      "Triangles and Quadrilaterals",
      "Areas and Heron's Formula",
      "Surface Areas and Volumes",
      "Statistics and Probability"
    ],
    English: [
      "Reading Comprehension",
      "Writing Skills",
      "Grammar and Usage",
      "Literature Appreciation",
      "Vocabulary Building",
      "Communication Skills"
    ],
    Foundation: [
      "Logical Reasoning",
      "Mental Ability",
      "Basic Problem Solving",
      "Analytical Thinking",
      "Pattern Recognition",
      "Critical Thinking"
    ]
  };

  const facultyMembers = [
    {
      name: "Prof. Anjali Sharma",
      subject: "Science",
      experience: "15+ Years",
      qualification: "M.Sc. Physics, B.Ed.",
      specialization: "Conceptual Physics & Chemistry",
      achievements: "Foundation Expert"
    },
    {
      name: "Dr. Rakesh Kumar",
      subject: "Mathematics",
      experience: "12+ Years", 
      qualification: "M.Sc. Mathematics, Ph.D.",
      specialization: "Algebra & Geometry",
      achievements: "Mathematics Teaching Expert"
    },
    {
      name: "Prof. Priya Singh",
      subject: "English & Foundation",
      experience: "10+ Years",
      qualification: "M.A. English, B.Ed.",
      specialization: "Language Skills & Reasoning",
      achievements: "Communication Expert"
    }
  ];

  const studyFeatures = [
    {
      title: "NCERT Foundation",
      description: "Complete NCERT syllabus coverage with additional practice",
      icon: <BookOpen className="h-6 w-6" />
    },
    {
      title: "Concept Building",
      description: "Strong fundamental concepts for future competitive success",
      icon: <Target className="h-6 w-6" />
    },
    {
      title: "Interactive Learning",
      description: "Engaging teaching methods to maintain student interest",
      icon: <Users className="h-6 w-6" />
    },
    {
      title: "Regular Assessment",
      description: "Continuous evaluation and feedback for improvement",
      icon: <Award className="h-6 w-6" />
    }
  ];

  const successStats = [
    { number: "New", label: "Institute", subtext: "Established 2025" },
    { number: "Expert", label: "Faculty", subtext: "Experienced Teachers" },
    { number: "Foundation", label: "Focus", subtext: "Strong Basics" },
    { number: "Quality", label: "Education", subtext: "Modern Teaching" }
  ];

  const batchDetails = [
    {
      name: "Foundation Batch A",
      duration: "1 Year (Class IX)",
      timing: "Morning: 7:00 AM - 11:00 AM",
      strength: "30 Students",
      features: ["All Subjects", "Board Preparation", "Foundation Building"]
    },
    {
      name: "Foundation Batch B", 
      duration: "1 Year (Class IX)",
      timing: "Afternoon: 2:00 PM - 6:00 PM",
      strength: "30 Students",
      features: ["All Subjects", "Interactive Sessions", "Regular Tests"]
    }
  ];

  const benefits = [
    "Strong conceptual foundation for all subjects",
    "Early exposure to competitive exam patterns",
    "Development of analytical and logical thinking",
    "Comprehensive board exam preparation",
    "Regular parent-teacher interaction",
    "Study material and practice sheets",
    "Doubt clearing sessions",
    "Personality development activities"
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-emerald-50 to-green-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-8">
            <Link to="/courses" className="flex items-center text-emerald-600 hover:text-emerald-700 font-medium">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Courses
            </Link>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="bg-emerald-600 p-3 rounded-xl mr-4">
                  <GraduationCap className="h-8 w-8 text-white" />
                </div>
                <span className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium">
                  Class IX Foundation
                </span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Foundation Building Program
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Build a strong academic foundation in Class IX with comprehensive coverage of all subjects
                and early introduction to competitive exam concepts for future success.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/admission"
                  className="bg-emerald-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 text-center"
                >
                  Enroll Now
                </Link>
                <a
                  href="tel:+919058619887"
                  className="border-2 border-emerald-600 text-emerald-600 px-8 py-3 rounded-lg font-semibold hover:bg-emerald-50 transition-colors duration-200 text-center flex items-center justify-center"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Call for Info
                </a>
              </div>
            </div>
            
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Class IX Students Building Strong Foundation with NCERT Books"
                className="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/50 to-transparent rounded-2xl"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <h3 className="text-2xl font-bold mb-2">Foundation Success</h3>
                <p className="text-emerald-100">Building Tomorrow's Leaders</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-6">
                {successStats.map((stat, index) => (
                  <div key={index} className="bg-white p-4 rounded-xl shadow-lg text-center">
                    <div className="text-2xl font-bold text-emerald-600 mb-1">{stat.number}</div>
                    <div className="font-semibold text-gray-900 text-sm mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-600">{stat.subtext}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Course Highlights */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Foundation Building?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Early foundation building is crucial for long-term academic success
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-6">
            {courseHighlights.map((highlight, index) => (
              <div key={index} className="bg-gradient-to-br from-emerald-50 to-green-50 p-6 rounded-xl text-center">
                <CheckCircle className="h-8 w-8 text-emerald-600 mx-auto mb-3" />
                <p className="text-gray-700 font-medium">{highlight}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Syllabus Coverage */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Complete Syllabus Coverage
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive curriculum covering all Class IX subjects
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {Object.entries(subjects).map(([subject, topics]) => (
              <div key={subject} className="bg-white rounded-2xl p-6 shadow-lg">
                <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">{subject}</h3>
                <ul className="space-y-2">
                  {topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-emerald-500 mr-2 mt-1 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Educational Resources Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Study Materials & Resources
            </h2>
            <p className="text-xl text-gray-600">
              NCERT-based curriculum with comprehensive foundation materials
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-12">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">NCERT Foundation Excellence</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Complete NCERT Coverage</h4>
                    <p className="text-gray-600">All NCERT textbooks for Class IX with additional practice</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Foundation Building</h4>
                    <p className="text-gray-600">Strong conceptual foundation for future competitive success</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Interactive Learning</h4>
                    <p className="text-gray-600">Modern teaching methods with practical demonstrations</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Class IX NCERT Mathematics and Science Books"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Students Learning Foundation Concepts"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1434494878577-86c23bcb06b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Educational Foundation Building Environment"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
              <img
                src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Class IX Learning and Education"
                className="w-full h-48 object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Study Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Study Features
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive learning approach for Class IX students
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {studyFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300">
                <div className="bg-emerald-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <div className="text-emerald-600">{feature.icon}</div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Faculty Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Expert Faculty Team
            </h2>
            <p className="text-xl text-gray-600">
              Experienced teachers dedicated to foundation building
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {facultyMembers.map((faculty, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-emerald-50 rounded-2xl p-6 shadow-lg text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-10 w-10 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">{faculty.name}</h3>
                <div className="text-emerald-600 font-semibold mb-2">{faculty.subject}</div>
                <div className="text-sm text-gray-600 mb-4">
                  <div>{faculty.qualification}</div>
                  <div>{faculty.experience}</div>
                </div>
                
                <div className="bg-white p-3 rounded-lg mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-1">Specialization</div>
                  <div className="text-sm text-gray-600">{faculty.specialization}</div>
                </div>
                
                <div className="flex items-center justify-center text-sm text-emerald-600 font-semibold">
                  <Trophy className="h-4 w-4 mr-1" />
                  {faculty.achievements}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Batch Details */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Batches
            </h2>
            <p className="text-xl text-gray-600">
              Choose the batch timing that works best for you
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {batchDetails.map((batch, index) => (
              <div key={index} className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-6 shadow-lg">
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-emerald-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{batch.name}</h3>
                </div>
                
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700 font-medium">Duration:</span>
                    <span className="text-emerald-600 font-semibold">{batch.duration}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700 font-medium">Timing:</span>
                    <span className="text-emerald-600 font-semibold">{batch.timing}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700 font-medium">Batch Size:</span>
                    <span className="text-emerald-600 font-semibold">{batch.strength}</span>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                  <ul className="space-y-1">
                    {batch.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Program Benefits
            </h2>
            <p className="text-xl text-gray-600">
              Comprehensive advantages of our Foundation Building program
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-gradient-to-br from-emerald-50 to-green-50 p-4 rounded-xl text-center">
                <CheckCircle className="h-6 w-6 text-emerald-600 mx-auto mb-3" />
                <p className="text-gray-700 text-sm font-medium">{benefit}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-green-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Build a Strong Foundation?
          </h2>
          <p className="text-xl text-emerald-100 mb-8 max-w-3xl mx-auto">
            Join our Foundation Building program and set the stage for future academic success
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/admission"
              className="bg-white text-emerald-600 px-8 py-3 rounded-lg font-semibold hover:bg-emerald-50 transition-colors duration-200"
            >
              Enroll Now
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-emerald-600 transition-colors duration-200"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FoundationBuilding;
